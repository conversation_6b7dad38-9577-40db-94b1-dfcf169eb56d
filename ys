[1mdiff --git a/src/templates/schedule.html b/src/templates/schedule.html[m
[1mindex f566ce5..2315190 100644[m
[1m--- a/src/templates/schedule.html[m
[1m+++ b/src/templates/schedule.html[m
[36m@@ -765,8 +765,8 @@[m
                                     <span id="display-priority">5.0</span>[m
                                 </div>[m
                                 <div class="parameter-display">[m
[31m-                                    <label>Weather Safety Weight:</label>[m
[31m-                                    <span id="display-weather">5.0</span>[m
[32m+[m[32m                                    <label>Utilization Weight:</label>[m
[32m+[m[32m                                    <span id="display-weather">2.0</span>[m
                                 </div>[m
                                 <div class="parameter-display">[m
                                     <label>Planning Horizon:</label>[m
[36m@@ -1428,49 +1428,80 @@[m
         return s;[m
     }[m
 [m
[31m-    // Optimization preset configurations[m
[31m-    const optimizationPresets = {[m
[32m+[m[32m    // Dynamic optimization preset configurations loaded from settings[m
[32m+[m[32m    let optimizationPresets = {[m
[32m+[m[32m        // Default fallback presets (will be replaced by settings API)[m
         throughput: {[m
             name: "🚀 Maximum Throughput",[m
             description: "Prioritizes maximizing the number of vessels processed within the planning horizon. Ideal for high-demand periods when throughput is critical.",[m
[31m-            details: { throughput: "High", cost: "Medium", safety: "Medium" },[m
[32m+[m[32m            details: { throughput: "High", cost: "Medium", infrastructure: "Medium" },[m
             weights: {[m
                 throughput: 15.0,[m
                 demurrage: 3.0,[m
                 priority: 5.0,[m
[31m-                weather: 5.0,[m
[32m+[m[32m                utilization: 2.0,[m
                 horizon: 7,[m
                 granularity: 1[m
             }[m
         },[m
         cost: {[m
[31m-            name: "💰 Cost Optimized",[m
[31m-            description: "Focuses on minimizing demurrage costs and operational expenses. Best for periods when cost control is the primary concern.",[m
[31m-            details: { throughput: "Medium", cost: "High", safety: "Low" },[m
[32m+[m[32m            name: "💰 Cost Efficiency",[m
[32m+[m[32m            description: "Focuses on minimizing demurrage costs and operational expenses. Best for cost-controlled operations.",[m
[32m+[m[32m            details: { throughput: "Medium", cost: "High", infrastructure: "Medium" },[m
             weights: {[m
                 throughput: 8.0,[m
                 demurrage: 18.0,[m
                 priority: 2.0,[m
[31m-                weather: 1.0,[m
[32m+[m[32m                utilization: 3.0,[m
                 horizon: 14,[m
                 granularity: 2[m
             }[m
         },[m
[31m-        safety: {[m
[31m-            name: "🛡️ Safety First",[m
[31m-            description: "Prioritizes weather safety and customer priority requirements. Recommended during adverse weather conditions or for high-value customers.",[m
[31m-            details: { throughput: "Low", cost: "Medium", safety: "High" },[m
[32m+[m[32m        infrastructure: {[m
[32m+[m[32m            name: "🏗️ Infrastructure Efficiency",[m
[32m+[m[32m            description: "Optimizes jetty utilization and minimizes infrastructure conflicts. Ideal for maximizing facility efficiency.",[m
[32m+[m[32m            details: { throughput: "Medium", cost: "Medium", infrastructure: "High" },[m
             weights: {[m
[31m-                throughput: 5.0,[m
[32m+[m[32m                throughput: 10.0,[m
                 demurrage: 8.0,[m
[31m-                priority: 15.0,[m
[31m-                weather: 12.0,[m
[32m+[m[32m                priority: 6.0,[m
[32m+[m[32m                utilization: 15.0,[m
                 horizon: 10,[m
                 granularity: 1[m
             }[m
[32m+[m[32m        },[m
[32m+[m[32m        balanced: {[m
[32m+[m[32m            name: "⚖️ Balanced",[m
[32m+[m[32m            description: "Balanced approach across all optimization factors. Good general-purpose optimization strategy.",[m
[32m+[m[32m            details: { throughput: "Medium", cost: "Medium", infrastructure: "Medium" },[m
[32m+[m[32m            weights: {[m
[32m+[m[32m                throughput: 12.0,[m
[32m+[m[32m                demurrage: 10.0,[m
[32m+[m[32m                priority: 8.0,[m
[32m+[m[32m                utilization: 8.0,[m
[32m+[m[32m                horizon: 7,[m
[32m+[m[32m                granularity: 1[m
[32m+[m[32m            }[m
         }[m
     };[m
 [m
[32m+[m[32m    // Load optimization presets from settings API[m
[32m+[m[32m    async function loadOptimizationPresets() {[m
[32m+[m[32m        try {[m
[32m+[m[32m            const response = await fetch('/api/settings/optimization-presets');[m
[32m+[m[32m            if (response.ok) {[m
[32m+[m[32m                const settingsPresets = await response.json();[m
[32m+[m[32m                // Merge with defaults, prioritizing settings[m
[32m+[m[32m                optimizationPresets = { ...optimizationPresets, ...settingsPresets };[m
[32m+[m[32m                console.log('Loaded optimization presets from settings:', optimizationPresets);[m
[32m+[m[32m            } else {[m
[32m+[m[32m                console.warn('Failed to load optimization presets from settings, using defaults');[m
[32m+[m[32m            }[m
[32m+[m[32m        } catch (error) {[m
[32m+[m[32m            console.warn('Error loading optimization presets:', error);[m
[32m+[m[32m        }[m
[32m+[m[32m    }[m
[32m+[m
     // Current optimization parameters (will be updated by preset selection)[m
     let currentOptimizationParams = { ...optimizationPresets.throughput.weights };[m
 [m
[36m@@ -1509,6 +1540,11 @@[m
     // Update preset description and parameter displays[m
     function updatePresetDisplay(presetKey) {[m
         const preset = optimizationPresets[presetKey];[m
[32m+[m[32m        if (!preset) {[m
[32m+[m[32m            console.error('Preset not found:', presetKey);[m
[32m+[m[32m            return;[m
[32m+[m[32m        }[m
[32m+[m[41m        [m
         const descriptionDiv = document.getElementById('preset-description');[m
 [m
         descriptionDiv.innerHTML = `[m
[36m@@ -1518,7 +1554,7 @@[m
                 <div class="preset-details">[m
                     <span class="detail-item">Throughput: <strong>${preset.details.throughput}</strong></span>[m
                     <span class="detail-item">Cost Control: <strong>${preset.details.cost}</strong></span>[m
[31m-                    <span class="detail-item">Safety: <strong>${preset.details.safety}</strong></span>[m
[32m+[m[32m                    <span class="detail-item">Infrastructure: <strong>${preset.details.infrastructure}</strong></span>[m
                 </div>[m
             </div>[m
         `;[m
[36m@@ -1527,7 +1563,7 @@[m
         document.getElementById('display-throughput').textContent = preset.weights.throughput.toFixed(1);[m
         document.getElementById('display-demurrage').textContent = preset.weights.demurrage.toFixed(1);[m
         document.getElementById('display-priority').textContent = preset.weights.priority.toFixed(1);[m
[31m-        document.getElementById('display-weather').textContent = preset.weights.weather.toFixed(1);[m
[32m+[m[32m        document.getElementById('display-weather').textContent = (preset.weights.utilization || 0).toFixed(1);[m
         document.getElementById('display-horizon').textContent = preset.weights.horizon + ' Days';[m
         document.getElementById('display-granularity').textContent = preset.weights.granularity + ' Hour' + (preset.weights.granularity > 1 ? 's' : '');[m
 [m

                   SSUUMMMMAARRYY OOFF LLEESSSS CCOOMMMMAANNDDSS

      Commands marked with * may be preceded by a number, _N.
      Notes in parentheses indicate the behavior if _N is given.
      A key preceded by a caret indicates the Ctrl key; thus ^K is ctrl-K.

  h  H                 Display this help.
  q  :q  Q  :Q  ZZ     Exit.
 ---------------------------------------------------------------------------

                           MMOOVVIINNGG

  e  ^E  j  ^N  CR  *  Forward  one line   (or _N lines).
  y  ^Y  k  ^K  ^P  *  Backward one line   (or _N lines).
  f  ^F  ^V  SPACE  *  Forward  one window (or _N lines).
  b  ^B  ESC-v      *  Backward one window (or _N lines).
  z                 *  Forward  one window (and set window to _N).
  w                 *  Backward one window (and set window to _N).
  ESC-SPACE         *  Forward  one window, but don't stop at end-of-file.
  d  ^D             *  Forward  one half-window (and set half-window to _N).
  u  ^U             *  Backward one half-window (and set half-window to _N).
  ESC-)  RightArrow *  Right one half screen width (or _N positions).
  ESC-(  LeftArrow  *  Left  one half screen width (or _N positions).
  ESC-}  ^RightArrow   Right to last column displayed.
  ESC-{  ^LeftArrow    Left  to first column.
  F                    Forward forever; like "tail -f".
  ESC-F                Like F but stop when search pattern is found.
  r  ^R  ^L            Repaint screen.
  R                    Repaint screen, discarding buffered input.
        ---------------------------------------------------
        Default "window" is the screen height.
        Default "half-window" is half of the screen height.
 ---------------------------------------------------------------------------

                          SSEEAARRCCHHIINNGG

  /_p_a_t_t_e_r_n          *  Search forward for (_N-th) matching line.
  ?_p_a_t_t_e_r_n          *  Search backward for (_N-th) matching line.
  n                 *  Repeat previous search (for _N-th occurrence).
  N                 *  Repeat previous search in reverse direction.
  ESC-n             *  Repeat previous search, spanning files.
  ESC-N             *  Repeat previous search, reverse dir. & spanning files.
  ^O^N  ^On         *  Search forward for (_N-th) OSC8 hyperlink.
  ^O^P  ^Op         *  Search backward for (_N-th) OSC8 hyperlink.
  ^O^L  ^Ol            Jump to the currently selected OSC8 hyperlink.
  ESC-u                Undo (toggle) search highlighting.
  ESC-U                Clear search highlighting.
  &_p_a_t_t_e_r_n          *  Display only matching lines.
        ---------------------------------------------------
        A search pattern may begin with one or more of:
        ^N or !  Search for NON-matching lines.
        ^E or *  Search multiple files (pass thru END OF FILE).
        ^F or @  Start search at FIRST file (for /) or last file (for ?).
        ^K       Highlight matches, but don't move (KEEP position).
        ^R       Don't use REGULAR EXPRESSIONS.
        ^S _n     Search for match in _n-th parenthesized subpattern.
        ^W       WRAP search if no match found.
        ^L       Enter next character literally into pattern.
 ---------------------------------------------------------------------------

                           JJUUMMPPIINNGG

  g  <  ESC-<       *  Go to first line in file (or line _N).
  G  >  ESC->       *  Go to last line in file (or line _N).
  p  %              *  Go to beginning of file (or _N percent into file).
  t                 *  Go to the (_N-th) next tag.
  T                 *  Go to the (_N-th) previous tag.
  {  (  [           *  Find close bracket } ) ].
  }  )  ]           *  Find open bracket { ( [.
  ESC-^F _<_c_1_> _<_c_2_>  *  Find close bracket _<_c_2_>.
  ESC-^B _<_c_1_> _<_c_2_>  *  Find open bracket _<_c_1_>.
        ---------------------------------------------------
        Each "find close bracket" command goes forward to the close bracket 
          matching the (_N-th) open bracket in the top line.
        Each "find open bracket" command goes backward to the open bracket 
          matching the (_N-th) close bracket in the bottom line.

  m_<_l_e_t_t_e_r_>            Mark the current top line with <letter>.
  M_<_l_e_t_t_e_r_>            Mark the current bottom line with <letter>.
  '_<_l_e_t_t_e_r_>            Go to a previously marked position.
  ''                   Go to the previous position.
  ^X^X                 Same as '.
  ESC-m_<_l_e_t_t_e_r_>        Clear a mark.
        ---------------------------------------------------
        A mark is any upper-case or lower-case letter.
        Certain marks are predefined:
             ^  means  beginning of the file
             $  means  end of the file
 ---------------------------------------------------------------------------

                        CCHHAANNGGIINNGG FFIILLEESS

  :e [_f_i_l_e]            Examine a new file.
  ^X^V                 Same as :e.
  :n                *  Examine the (_N-th) next file from the command line.
  :p                *  Examine the (_N-th) previous file from the command line.
  :x                *  Examine the first (or _N-th) file from the command line.
  ^O^O                 Open the currently selected OSC8 hyperlink.
  :d                   Delete the current file from the command line list.
  =  ^G  :f            Print current file name.
 ---------------------------------------------------------------------------

                    MMIISSCCEELLLLAANNEEOOUUSS CCOOMMMMAANNDDSS

  -_<_f_l_a_g_>              Toggle a command line option [see OPTIONS below].
  --_<_n_a_m_e_>             Toggle a command line option, by name.
  __<_f_l_a_g_>              Display the setting of a command line option.
  ___<_n_a_m_e_>             Display the setting of an option, by name.
  +_c_m_d                 Execute the less cmd each time a new file is examined.

  !_c_o_m_m_a_n_d             Execute the shell command with $SHELL.
  #_c_o_m_m_a_n_d             Execute the shell command, expanded like a prompt.
  |XX_c_o_m_m_a_n_d            Pipe file between current pos & mark XX to shell command.
  s _f_i_l_e               Save input to a file.
  v                    Edit the current file with $VISUAL or $EDITOR.
  V                    Print version number of "less".
 ---------------------------------------------------------------------------

                           OOPPTTIIOONNSS

        Most options may be changed either on the command line,
        or from within less by using the - or -- command.
        Options may be given in one of two forms: either a single
        character preceded by a -, or a name preceded by --.

  -?  ........  --help
                  Display help (from command line).
  -a  ........  --search-skip-screen
                  Search skips current screen.
  -A  ........  --SEARCH-SKIP-SCREEN
                  Search starts just after target line.
  -b [_N]  ....  --buffers=[_N]
                  Number of buffers.
  -B  ........  --auto-buffers
                  Don't automatically allocate buffers for pipes.
  -c  ........  --clear-screen
                  Repaint by clearing rather than scrolling.
  -d  ........  --dumb
                  Dumb terminal.
  -D xx_c_o_l_o_r  .  --color=xx_c_o_l_o_r
                  Set screen colors.
  -e  -E  ....  --quit-at-eof  --QUIT-AT-EOF
                  Quit at end of file.
  -f  ........  --force
                  Force open non-regular files.
  -F  ........  --quit-if-one-screen
                  Quit if entire file fits on first screen.
  -g  ........  --hilite-search
                  Highlight only last match for searches.
  -G  ........  --HILITE-SEARCH
                  Don't highlight any matches for searches.
  -h [_N]  ....  --max-back-scroll=[_N]
                  Backward scroll limit.
  -i  ........  --ignore-case
                  Ignore case in searches that do not contain uppercase.
  -I  ........  --IGNORE-CASE
                  Ignore case in all searches.
  -j [_N]  ....  --jump-target=[_N]
                  Screen position of target lines.
  -J  ........  --status-column
                  Display a status column at left edge of screen.
  -k _f_i_l_e  ...  --lesskey-file=_f_i_l_e
                  Use a compiled lesskey file.
  -K  ........  --quit-on-intr
                  Exit less in response to ctrl-C.
  -L  ........  --no-lessopen
                  Ignore the LESSOPEN environment variable.
  -m  -M  ....  --long-prompt  --LONG-PROMPT
                  Set prompt style.
  -n .........  --line-numbers
                  Suppress line numbers in prompts and messages.
  -N .........  --LINE-NUMBERS
                  Display line number at start of each line.
  -o [_f_i_l_e] ..  --log-file=[_f_i_l_e]
                  Copy to log file (standard input only).
  -O [_f_i_l_e] ..  --LOG-FILE=[_f_i_l_e]
                  Copy to log file (unconditionally overwrite).
  -p _p_a_t_t_e_r_n .  --pattern=[_p_a_t_t_e_r_n]
                  Start at pattern (from command line).
  -P [_p_r_o_m_p_t]   --prompt=[_p_r_o_m_p_t]
                  Define new prompt.
  -q  -Q  ....  --quiet  --QUIET  --silent --SILENT
                  Quiet the terminal bell.
  -r  -R  ....  --raw-control-chars  --RAW-CONTROL-CHARS
                  Output "raw" control characters.
  -s  ........  --squeeze-blank-lines
                  Squeeze multiple blank lines.
  -S  ........  --chop-long-lines
                  Chop (truncate) long lines rather than wrapping.
  -t _t_a_g  ....  --tag=[_t_a_g]
                  Find a tag.
  -T [_t_a_g_s_f_i_l_e] --tag-file=[_t_a_g_s_f_i_l_e]
                  Use an alternate tags file.
  -u  -U  ....  --underline-special  --UNDERLINE-SPECIAL
                  Change handling of backspaces, tabs and carriage returns.
  -V  ........  --version
                  Display the version number of "less".
  -w  ........  --hilite-unread
                  Highlight first new line after forward-screen.
  -W  ........  --HILITE-UNREAD
                  Highlight first new line after any forward movement.
  -x [_N[,...]]  --tabs=[_N[,...]]
                  Set tab stops.
  -X  ........  --no-init
                  Don't use termcap init/deinit strings.
  -y [_N]  ....  --max-forw-scroll=[_N]
                  Forward scroll limit.
  -z [_N]  ....  --window=[_N]
                  Set size of window.
  -" [_c[_c]]  .  --quotes=[_c[_c]]
                  Set shell quote characters.
  -~  ........  --tilde
                  Don't display tildes after end of file.
  -# [_N]  ....  --shift=[_N]
                  Set horizontal scroll amount (0 = one half screen width).

                --exit-follow-on-close
                  Exit F command on a pipe when writer closes pipe.
                --file-size
                  Automatically determine the size of the input file.
                --follow-name
                  The F command changes files if the input file is renamed.
                --header=[_L[,_C[,_N]]]
                  Use _L lines (starting at line _N) and _C columns as headers.
                --incsearch
                  Search file as each pattern character is typed in.
                --intr=[_C]
                  Use _C instead of ^X to interrupt a read.
                --lesskey-context=_t_e_x_t
                  Use lesskey source file contents.
                --lesskey-src=_f_i_l_e
                  Use a lesskey source file.
                --line-num-width=[_N]
                  Set the width of the -N line number field to _N characters.
                --match-shift=[_N]
                  Show at least _N characters to the left of a search match.
                --modelines=[_N]
                  Read _N lines from the input file and look for vim modelines.
                --mouse
                  Enable mouse input.
                --no-keypad
                  Don't send termcap keypad init/deinit strings.
                --no-histdups
                  Remove duplicates from command history.
                --no-number-headers
                  Don't give line numbers to header lines.
                --no-search-header-lines
                  Searches do not include header lines.
                --no-search-header-columns
                  Searches do not include header columns.
                --no-search-headers
                  Searches do not include header lines or columns.
                --no-vbell
                  Disable the terminal's visual bell.
                --redraw-on-quit
                  Redraw final screen when quitting.
                --rscroll=[_C]
                  Set the character used to mark truncated lines.
                --save-marks
                  Retain marks across invocations of less.
                --search-options=[EFKNRW-]
                  Set default options for every search.
                --show-preproc-errors
                  Display a message if preprocessor exits with an error status.
                --proc-backspace
                  Process backspaces for bold/underline.
                --PROC-BACKSPACE
                  Treat backspaces as control characters.
                --proc-return
                  Delete carriage returns before newline.
                --PROC-RETURN
                  Treat carriage returns as control characters.
                --proc-tab
                  Expand tabs to spaces.
                --PROC-TAB
                  Treat tabs as control characters.
                --status-col-width=[_N]
                  Set the width of the -J status column to _N characters.
                --status-line
                  Highlight or color the entire line containing a mark.
                --use-backslash
                  Subsequent options use backslash as escape char.
                --use-color
                  Enables colored text.
                --wheel-lines=[_N]
                  Each click of the mouse wheel moves _N lines.
                --wordwrap
                  Wrap lines at spaces.


 ---------------------------------------------------------------------------

                          LLIINNEE EEDDIITTIINNGG

        These keys can be used to edit text being entered 
        on the "command line" at the bottom of the screen.

 RightArrow ..................... ESC-l ... Move cursor right one character.
 LeftArrow ...................... ESC-h ... Move cursor left one character.
 ctrl-RightArrow  ESC-RightArrow  ESC-w ... Move cursor right one word.
 ctrl-LeftArrow   ESC-LeftArrow   ESC-b ... Move cursor left one word.
 HOME ........................... ESC-0 ... Move cursor to start of line.
 END ............................ ESC-$ ... Move cursor to end of line.
 BACKSPACE ................................ Delete char to left of cursor.
 DELETE ......................... ESC-x ... Delete char under cursor.
 ctrl-BACKSPACE   ESC-BACKSPACE ........... Delete word to left of cursor.
 ctrl-DELETE .... ESC-DELETE .... ESC-X ... Delete word under cursor.
 ctrl-U ......... ESC (MS-DOS only) ....... Delete entire line.
 UpArrow ........................ ESC-k ... Retrieve previous command line.
 DownArrow ...................... ESC-j ... Retrieve next command line.
 TAB ...................................... Complete filename & cycle.
 SHIFT-TAB ...................... ESC-TAB   Complete filename & reverse cycle.
 ctrl-L ................................... Complete filename, list all.
