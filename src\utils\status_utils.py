"""
Status Utilities Module

This module provides utility functions for normalizing, validating, and checking 
transitions for vessel and assignment statuses.
"""

import re
import logging
from typing import Tuple, Dict, List, Set

# Define valid statuses
VALID_VESSEL_STATUSES = {
    "EN_ROUTE",      # <PERSON><PERSON><PERSON> is en route to the terminal
    "APPROACHING",   # <PERSON><PERSON><PERSON> is approaching the terminal
    "ARRIVED",       # <PERSON><PERSON><PERSON> has arrived and is waiting
    "WAITING",       # <PERSON><PERSON><PERSON> is waiting for availability
    "DOCKED",        # <PERSON><PERSON><PERSON> is docked at a jetty
    "DEPARTED"       # <PERSON><PERSON><PERSON> has departed
}

VALID_ASSIGNMENT_STATUSES = {
    "PENDING_APPROVAL",  # Assignment is pending approval
    "APPROVED",          # Assignment has been approved
    "SCHEDULED",         # Assignment is scheduled but not yet active
    "ACTIVE",            # Assignment is currently active (synonym: IN_PROGRESS)
    "IN_PROGRESS",       # Synonym for ACTIVE
    "COMPLETED",         # Assignment has been completed
    "CANCELLED"          # Assignment has been cancelled
}

# Define valid status transitions (from -> set of valid to statuses)
VALID_VESSEL_TRANSITIONS: Dict[str, Set[str]] = {
    "EN_ROUTE": {"APPROACHING"},
    "APPROACHING": {"ARRIVED", "EN_ROUTE", "WAITING"},
    "ARRIVED": {"DOCKED", "DEPARTED", "APPROACHING", "WAITING"},
    "WAITING": {"DOCKED", "DEPARTED", "APPROACHING"},
    "DOCKED": {"DEPARTED"},
    "DEPARTED": {"EN_ROUTE", "APPROACHING"}  # Can circle back for another visit
}

VALID_ASSIGNMENT_TRANSITIONS: Dict[str, Set[str]] = {
    "PENDING_APPROVAL": {"APPROVED", "SCHEDULED", "IN_PROGRESS", "CANCELLED"},
    "APPROVED": {"SCHEDULED", "IN_PROGRESS", "ACTIVE", "CANCELLED"},
    "SCHEDULED": {"IN_PROGRESS", "ACTIVE", "CANCELLED"},
    "ACTIVE": {"COMPLETED", "CANCELLED"},
    "IN_PROGRESS": {"COMPLETED", "CANCELLED"},
    "COMPLETED": {},  # Terminal state
    "CANCELLED": {}   # Terminal state
}


def normalize_status(status: str) -> str:
    """
    Normalize a status string to standardized format.
    
    Args:
        status: The status string to normalize
        
    Returns:
        Normalized status string
    """
    if not status:
        return ""
    
    # Trim whitespace and convert to uppercase
    status = status.strip().upper()
    
    # Replace spaces with underscores for multi-word statuses
    status = re.sub(r'\s+', '_', status)
    
    return status


def is_valid_vessel_status(status: str) -> bool:
    """
    Check if a vessel status is valid.
    
    Args:
        status: The status to check
        
    Returns:
        True if valid, False otherwise
    """
    return status in VALID_VESSEL_STATUSES


def is_valid_assignment_status(status: str) -> bool:
    """
    Check if an assignment status is valid.
    
    Args:
        status: The status to check
        
    Returns:
        True if valid, False otherwise
    """
    return status in VALID_ASSIGNMENT_STATUSES


def is_valid_vessel_transition(from_status: str, to_status: str) -> Tuple[bool, str]:
    """
    Check if a vessel status transition is valid.
    
    Args:
        from_status: Current status
        to_status: Target status
        
    Returns:
        Tuple of (is_valid, message)
    """
    # Check if both statuses are valid
    if not is_valid_vessel_status(from_status):
        return False, f"Invalid current status: {from_status}"
    
    if not is_valid_vessel_status(to_status):
        return False, f"Invalid target status: {to_status}"
    
    # Allow same status (no change)
    if from_status == to_status:
        return True, ""
    
    # Check if transition is allowed
    if to_status in VALID_VESSEL_TRANSITIONS.get(from_status, set()):
        return True, ""
    
    return False, f"Invalid vessel transition from {from_status} to {to_status}"


def is_valid_assignment_transition(from_status: str, to_status: str) -> Tuple[bool, str]:
    """
    Check if an assignment status transition is valid.
    
    Args:
        from_status: Current status
        to_status: Target status
        
    Returns:
        Tuple of (is_valid, message)
    """
    # Check if both statuses are valid
    if not is_valid_assignment_status(from_status):
        return False, f"Invalid current status: {from_status}"
    
    if not is_valid_assignment_status(to_status):
        return False, f"Invalid target status: {to_status}"
    
    # Allow same status (no change)
    if from_status == to_status:
        return True, ""
    
    # Check if transition is allowed
    if to_status in VALID_ASSIGNMENT_TRANSITIONS.get(from_status, set()):
        return True, ""
    
    return False, f"Invalid assignment transition from {from_status} to {to_status}"


def get_valid_next_statuses(current_status: str, is_vessel: bool = True) -> List[str]:
    """
    Get list of valid next statuses from current status.
    
    Args:
        current_status: Current status
        is_vessel: Whether this is for a vessel (True) or an assignment (False)
        
    Returns:
        List of valid next statuses
    """
    normalized = normalize_status(current_status)
    
    # Determine which transition map to use
    transitions = VALID_VESSEL_TRANSITIONS if is_vessel else VALID_ASSIGNMENT_TRANSITIONS
    
    # Check if current status is valid
    if normalized not in transitions:
        logging.warning(f"Invalid current status: {current_status}")
        return []
    
    # Return valid next statuses
    return list(transitions.get(normalized, set())) 