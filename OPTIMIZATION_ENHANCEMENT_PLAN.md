# Optimization Enhancement Plan
*Advanced Scheduling Optimization with Assignment Locking & Selective Re-optimization*

## Overview

This plan outlines enhancements to the scheduling optimization system to support advanced workflows including assignment locking, selective re-optimization, and improved handling of mixed scheduling scenarios.

## Current State Analysis

### How Optimization Currently Works

1. **Vessel Selection**: Only considers vessels with status `EN_ROUTE`, `APPROACHING`, `ARRIVED`
2. **Assignment Handling**: 
   - `include_mock_assignments=False`: Ignores existing assignments (fresh optimization)
   - `include_mock_assignments=True`: Treats existing assignments as "reserved intervals" that block jetty time
3. **Result Processing**: Completely replaces all assignments in database with optimized results
4. **Status Updates**: Updates planned vessel statuses to reflect new assignments

### Current Optimization Scenarios

| Scenario | Current Implementation | Limitations |
|----------|----------------------|-------------|
| 1. Empty schedule | `include_mock_assignments=False` | ✅ Works well |
| 2. Add to existing | `include_mock_assignments=True` | ⚠️ Crude - treats all assignments as immutable |
| 3. Complete re-mix | Manual deletion + fresh optimization | ❌ No built-in support |
| 4. Preset changes | Re-run with different weights | ⚠️ No selectivity |
| 5. Locked assignments | Not supported | ❌ Missing feature |

## Enhanced Optimization Scenarios

### 1. Fresh Schedule (Empty Terminal)
**Use Case**: Start with completely empty schedule, optimize all unscheduled vessels.
**Current**: ✅ Supported via `include_mock_assignments=False`
**Enhancement**: Add confirmation dialog and better UI feedback.

### 2. Incremental Addition
**Use Case**: Add new nominations/vessels to existing schedule without changing locked assignments.
**Current**: ⚠️ Basic support via `include_mock_assignments=True`
**Enhancement**: Respect assignment lock status, better conflict resolution.

### 3. Selective Re-optimization  
**Use Case**: Re-optimize specific vessels or time windows while preserving locked assignments.
**Current**: ❌ Not supported
**Enhancement**: New optimization mode with vessel/time filtering.

### 4. Preset-based Re-optimization
**Use Case**: Apply different optimization weights to unlocked assignments only.
**Current**: ⚠️ Crude - affects all assignments
**Enhancement**: Respect locks, only re-optimize flexible assignments.

### 5. Assignment Protection Levels
**Use Case**: Different protection levels (locked, flexible, re-optimizable).
**Current**: ❌ Not supported  
**Enhancement**: Multi-level assignment protection system.

### 6. Conflict Resolution
**Use Case**: Handle conflicts when new vessels can't be scheduled around locked assignments.
**Current**: ❌ Poor handling
**Enhancement**: Smart conflict detection and resolution suggestions.

## Technical Implementation Plan

### Repository Integration Map

- Backend models: `src/db/models.py`
  - Add columns to `Assignment`: `lock_status` (VARCHAR), `lock_reason` (TEXT), `locked_by` (VARCHAR), `locked_at` (TIMESTAMP)
  - New model `OptimizationSession` (if sessions are persisted) with fields from the SQL block in Phase 1.2
  - Ensure relationships stay consistent; no changes needed to `Terminal`, `Vessel`, `Jetty` for locking

- API (FastAPI): `src/api/`
  - Locking routes: extend `assignment_api.py` with lock/unlock/bulk-lock and list-locks
  - Selective optimization routes: create `optimization_api.py` (new) or extend `ml_api.py` if optimization endpoints already reside there
  - App wiring in `src/api/fastapi_app.py`

- Scheduler core: `src/optimization/scheduler.py`
  - Add optional args: `locked_assignments`, `optimization_mode`, `vessel_filter`, `time_window_start/end`
  - Implement `_add_locked_constraints` to inject fixed intervals; reuse existing `reserved_intervals_by_jetty`
  - Implement `_filter_optimizable_vessels` for SELECTIVE/INCREMENTAL modes

- UI Templates and JS:
  - Template: `src/templates/schedule.html` add lock icons, bulk-selection UI, and mode selector
  - JS: add `src/static/js/optimization.js` for mode selection, progress, lock actions (keep presets in `settings.js`)
  - Optional CSS tweaks under `src/static/css/components/`

- Alembic migrations: `alembic/versions/` and `alembic.ini`
  - One migration to add assignment lock columns
  - One migration to add `optimization_sessions` table (if persisted)
  - Validate against `data/jetty_planner.db` (SQLite) and Postgres if applicable

- Tests: `src/tests/`
  - Unit tests for lock endpoints, scheduler honoring locks, and selective optimization filters

### Commands (PowerShell) for Migrations and Runs

```powershell
# Generate migration after updating SQLAlchemy models
alembic revision --autogenerate -m "assignment locking + optimization sessions"

# Apply migrations
alembic upgrade head

# (Optional) Downgrade if needed
alembic downgrade -1
```

Note: If environment variables are required for DB URL, export them for the session first, or configure `alembic.ini` appropriately.

### Phase 1: Database Schema Enhancements

#### 1.1 Assignment Locking Schema
```sql
ALTER TABLE assignments ADD COLUMN lock_status VARCHAR(20) DEFAULT 'UNLOCKED';
ALTER TABLE assignments ADD COLUMN lock_reason TEXT;
ALTER TABLE assignments ADD COLUMN locked_by VARCHAR(100);
ALTER TABLE assignments ADD COLUMN locked_at TIMESTAMP;
```

**Lock Status Values**:
- `UNLOCKED`: Can be modified/re-optimized freely
- `SOFT_LOCKED`: Protected from optimization but can be manually overridden  
- `HARD_LOCKED`: Cannot be modified (critical/confirmed assignments)
- `TIME_LOCKED`: Cannot change time slots but vessel/jetty can be swapped

#### 1.2 Optimization Sessions
```sql
CREATE TABLE optimization_sessions (
    id SERIAL PRIMARY KEY,
    terminal_id TEXT NOT NULL,
    session_type VARCHAR(50) NOT NULL, -- 'FRESH', 'INCREMENTAL', 'SELECTIVE', 'PRESET_CHANGE'
    parameters JSONB,
    vessel_filter JSONB, -- specific vessels to include/exclude
    time_window_start TIMESTAMP,
    time_window_end TIMESTAMP,
    preserve_locked BOOLEAN DEFAULT TRUE,
    started_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'RUNNING',
    result JSONB
);
```

#### 1.3 Repository Mapping and Migration Steps

- SQLAlchemy model updates in `src/db/models.py`:
  - Extend `Assignment` with: `lock_status: String(20) = 'UNLOCKED'`, `lock_reason: Text`, `locked_by: String(100)`, `locked_at: DateTime`
  - Add `OptimizationSession` model mirroring Phase 1.2 schema (optional if you prefer transient sessions only)
- Alembic migration sequence:
  - Create a single revision adding the four `assignments` columns
  - Create a separate revision for `optimization_sessions` if introduced
  - Verify autogeneration diffs, then upgrade

Data backfill/compatibility:
- Default all existing rows to `UNLOCKED`
- Backfill `locked_at` as `NULL` and keep nullable

### Phase 2: API Enhancements

#### 2.1 Enhanced OptimizationParameters
```python
class OptimizationParameters(BaseModel):
    # Existing parameters
    horizon_days: int = 7
    time_granularity_hours: int = 1
    weight_throughput: float = 10.0
    weight_demurrage: float = 5.0
    weight_priority: float = 3.0
    weight_weather: float = 2.0
    force_assign_all: bool = False
    
    # New parameters
    optimization_mode: str = "FRESH"  # FRESH, INCREMENTAL, SELECTIVE, PRESET_CHANGE
    preserve_locked: bool = True
    vessel_filter: Optional[List[str]] = None  # Specific vessel IDs to optimize
    assignment_filter: Optional[List[int]] = None  # Specific assignments to re-optimize
    time_window_start: Optional[datetime] = None
    time_window_end: Optional[datetime] = None
    conflict_resolution: str = "SUGGEST"  # SUGGEST, FORCE, ABORT
    lock_override_reason: Optional[str] = None
```

#### 2.2 Assignment Locking Endpoints
```python
@app.post("/api/schedule/assignments/{assignment_id}/lock")
async def lock_assignment(assignment_id: int, lock_request: AssignmentLockRequest)

@app.post("/api/schedule/assignments/{assignment_id}/unlock") 
async def unlock_assignment(assignment_id: int, unlock_request: AssignmentUnlockRequest)

@app.post("/api/schedule/assignments/bulk-lock")
async def bulk_lock_assignments(bulk_request: BulkLockRequest)

@app.get("/api/schedule/assignments/locks")
async def get_locked_assignments()
```

Repository mapping and file targets:
- Add Pydantic DTOs in `src/api/assignment_api.py` (e.g., `AssignmentLockRequest`, `AssignmentUnlockRequest`, `BulkLockRequest`)
- Add the routes in `src/api/assignment_api.py` and include router in `src/api/fastapi_app.py`
- For listing locks, expose filters by `terminal_id` and `lock_status`

DTOs and payloads:
```python
class AssignmentLockRequest(BaseModel):
    lock_status: Literal['SOFT_LOCKED','HARD_LOCKED','TIME_LOCKED']
    lock_reason: Optional[str] = None
    locked_by: Optional[str] = None  # user/email

class AssignmentUnlockRequest(BaseModel):
    unlock_reason: Optional[str] = None
    unlocked_by: Optional[str] = None

class BulkLockRequest(BaseModel):
    assignment_ids: List[int]
    lock_status: Literal['SOFT_LOCKED','HARD_LOCKED','TIME_LOCKED']
    lock_reason: Optional[str] = None
    locked_by: Optional[str] = None
```

Endpoint behaviors:
- `POST /api/schedule/assignments/{id}/lock`: Set fields on `assignments` and return updated record
- `POST /api/schedule/assignments/{id}/unlock`: Reset `lock_status` to `UNLOCKED`, clear `locked_by`, `locked_at` (keep `lock_reason` as history or move to change log)
- `POST /api/schedule/assignments/bulk-lock`: Bulk update
- `GET /api/schedule/assignments/locks?terminal_id=...&status=...`: Filtered list

#### 2.3 Selective Optimization Endpoints
```python
@app.post("/api/optimize/selective")
async def selective_optimization(params: SelectiveOptimizationParameters)

@app.post("/api/optimize/vessels")
async def optimize_specific_vessels(vessel_ids: List[str], params: OptimizationParameters)

@app.post("/api/optimize/time-window")
async def optimize_time_window(start: datetime, end: datetime, params: OptimizationParameters)
```

Repository mapping and file targets:
- Create `src/api/optimization_api.py` with routes for `selective`, `vessels`, `time-window`
- Wire router in `src/api/fastapi_app.py`
- Map DTO → `OptimizationParameters` and translate to scheduler args

DTOs and payloads:
```python
class OptimizationParameters(BaseModel):
    horizon_days: int = 7
    time_granularity_hours: int = 1
    weight_throughput: float = 10.0
    weight_demurrage: float = 5.0
    weight_priority: float = 3.0
    force_assign_all: bool = False
    optimization_mode: Literal['FRESH','INCREMENTAL','SELECTIVE','PRESET_CHANGE'] = 'FRESH'
    preserve_locked: bool = True
    vessel_filter: Optional[List[str]] = None
    assignment_filter: Optional[List[int]] = None
    time_window_start: Optional[datetime] = None
    time_window_end: Optional[datetime] = None
    conflict_resolution: Literal['SUGGEST','FORCE','ABORT'] = 'SUGGEST'
    lock_override_reason: Optional[str] = None
```

Responses should include:
- `session_id`, `status`, `message`, `percentage`, `currentActivity`, `changes_summary`, and optional `conflicts`

### Phase 3: Scheduler Core Enhancements

#### 3.1 Assignment Lock Handling
```python
class JettyScheduler:
    def __init__(self, ..., locked_assignments: List[Assignment] = None, 
                 optimization_mode: str = "FRESH"):
        self.locked_assignments = locked_assignments or []
        self.optimization_mode = optimization_mode
        
    def _add_locked_constraints(self, model: cp_model.CpModel):
        """Add constraints to respect locked assignments"""
        for locked_assignment in self.locked_assignments:
            # Create fixed intervals for locked assignments
            # These become immutable constraints in the optimization model
            pass
            
    def _filter_optimizable_vessels(self, vessels: List[VesselBase]) -> List[VesselBase]:
        """Filter vessels based on optimization mode and filters"""
        if self.optimization_mode == "SELECTIVE":
            # Only include specified vessels
            return [v for v in vessels if v.id in self.vessel_filter]
        return vessels
```

Repository mapping and file targets:
- In `src/optimization/scheduler.py`:
  - Add `locked_assignments` and `optimization_mode`
  - Implement `_add_locked_constraints(model)` by converting locked assignment windows to fixed intervals (similar to reserved intervals)
  - Ensure `optimize()` calls `_add_locked_constraints` before `AddNoOverlap`
  - Implement `_filter_optimizable_vessels` to honor `SELECTIVE`, `INCREMENTAL` filters

Implementation notes:
- Reuse `reserved_intervals_by_jetty` by merging in `[start_time, end_time)` from locked assignments where `lock_status != 'UNLOCKED'`
- For `TIME_LOCKED`, fix the interval but allow vessel/jetty switch only if time window identical; for v1, treat as fully fixed to simplify
- Add early filter: if `optimization_mode == 'SELECTIVE'` and `vessel_filter`, reduce candidate `vessels` set accordingly

#### 3.2 Conflict Detection & Resolution
```python
class ConflictDetector:
    def detect_conflicts(self, new_assignments: List[Assignment], 
                        locked_assignments: List[Assignment]) -> List[Conflict]:
        """Detect scheduling conflicts between new and locked assignments"""
        
    def suggest_resolutions(self, conflicts: List[Conflict]) -> List[ConflictResolution]:
        """Suggest ways to resolve conflicts"""
        # 1. Time shift suggestions
        # 2. Jetty alternatives  
        # 3. Vessel postponement options
        # 4. Lock override recommendations
```

Repository mapping and file targets:
- New helper in `src/optimization/conflicts.py` (or within `scheduler.py` initially)
- Provide deterministic conflict detection based on time/jetty overlaps with locked items

Conflict payload:
```json
{
  "conflicts": [
    {
      "type": "time_overlap",
      "jetty": "Jetty 1",
      "locked_assignment_id": 247,
      "candidate_vessel_id": "NV001",
      "overlap_minutes": 120,
      "suggestions": [
        {"action": "shift", "minutes": 60},
        {"action": "change_jetty", "options": ["Jetty 2"]}
      ]
    }
  ]
}
```

### Phase 4: UI Enhancements & User Experience

#### 4.1 Assignment Locking Interface
- **Lock Toggle Buttons**: Add lock/unlock buttons to assignment table
- **Lock Status Indicators**: Visual indicators (lock icons, colors) for locked assignments
- **Bulk Operations**: Select multiple assignments for bulk locking/unlocking
- **Lock Information**: Hover tooltips showing lock reason, who locked, when
- **Lock Confirmation**: Contextual dialogs explaining lock implications

#### 4.2 Optimization Mode Selection with Rich Feedback
```html
<div class="optimization-mode-selector">
    <label>Optimization Mode:</label>
    <select id="optimization-mode">
        <option value="FRESH">🆕 Fresh Schedule (Clear & Re-optimize)</option>
        <option value="INCREMENTAL">➕ Add New Vessels (Keep Existing)</option>
        <option value="SELECTIVE">🎯 Selective Re-optimization</option>
        <option value="PRESET_CHANGE">⚖️ Apply New Weights</option>
    </select>
    <div id="mode-explanation" class="mode-help-text">
        <!-- Dynamic explanation based on selected mode -->
    </div>
</div>
```

Repository mapping and file targets:
- Template updates in `src/templates/schedule.html`
- Add `src/static/js/optimization.js` to:
  - Handle mode selector, form to parameters mapping
  - Call lock/unlock/bulk-lock endpoints
  - Poll/subscribe to progress and update status panel
  - Integrate with existing presets from `settings.js` if `PRESET_CHANGE`

#### 4.3 Selective Optimization Controls
- **Vessel Selection**: Multi-select dropdown for choosing specific vessels
- **Time Window Picker**: Date/time range selector for optimization window
- **Assignment Filter**: Checkbox list for selecting assignments to re-optimize
- **Lock Override**: Warning dialog for operations affecting locked assignments

#### 4.4 Conflict Resolution Interface
- **Conflict Alert Panel**: Shows detected conflicts with severity levels
- **Resolution Suggestions**: List of suggested actions with one-click application
- **Manual Override Options**: Advanced controls for force-resolving conflicts

#### 4.5 Responsive User Feedback System

##### Real-time Status Indicators
```html
<div class="optimization-status-panel">
    <div class="status-indicator">
        <i class="fas fa-circle" id="optimization-status-icon"></i>
        <span id="optimization-status-text">Ready for optimization</span>
    </div>
    <div class="progress-section" id="optimization-progress" style="display: none;">
        <div class="progress-bar">
            <div class="progress-fill" id="optimization-progress-fill"></div>
        </div>
        <span class="progress-text" id="optimization-progress-text">0%</span>
    </div>
</div>
```

Repository mapping and file targets:
- `src/templates/schedule.html` for HTML blocks
- `src/static/js/optimization.js` for dynamic updates and WebSocket/polling logic
  - Use IDs: `optimization-mode`, `mode-explanation`, `optimization-status-icon`, `optimization-status-text`, `optimization-progress-fill`, `optimization-progress-text`, `current-activity-text`
  - Provide `startOptimization(params)` that POSTs and starts polling `/api/optimize/status/{session_id}`

WebSocket/polling endpoint suggestions:
- `GET /api/optimize/status/{session_id}` → progress JSON for polling
- `WS /ws/optimize/{session_id}` → push updates if WS is feasible later

### Operational Rollout & Testing

Pre-migration safety:
- Export SQLite snapshot: see `backups/` and scripts in `scripts/backup-*.ps1`
- Verify `.env`/DB URL for Alembic

PowerShell workflow (dev):
```powershell
# 1) Create and apply migrations
alembic revision --autogenerate -m "locking + sessions"
alembic upgrade head

# 2) Run API (adjust command to your entry point)
python -m src.main

# 3) Smoke tests (PowerShell Invoke-WebRequest)
iwr -UseBasicParsing http://localhost:8000/api/health
```

Verification checklist:
- Lock/unlock single and bulk; DB fields change and UI updates
- Run optimization with one assignment locked; verify scheduler preserves window
- SELECTIVE mode with 1–2 vessels; verify only those move
- PRESET_CHANGE: change weights and confirm different objective

Roll-back plan:
- `alembic downgrade -1` if migration issues
- Restore from latest `backups/jetty_planner_backup_*.db` if needed

##### Interactive Help & Explanations
- **Contextual Help**: Question mark icons with detailed explanations
- **Mode Descriptions**: Dynamic content explaining what each optimization mode does
- **Impact Previews**: Show estimated changes before applying optimization
- **Guided Workflows**: Step-by-step assistance for complex operations

##### Smart Warnings & Confirmations
```html
<div class="smart-warning-dialog">
    <div class="warning-header">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Optimization Impact</h3>
    </div>
    <div class="warning-content">
        <p>This optimization will affect:</p>
        <ul id="impact-list">
            <li>🔄 <strong>3 vessels</strong> will be rescheduled</li>
            <li>🔒 <strong>2 locked assignments</strong> will be preserved</li>
            <li>⏰ <strong>1 assignment</strong> may be delayed by 4 hours</li>
        </ul>
        <div class="estimated-improvement">
            <strong>Estimated Improvement:</strong> 
            <span class="improvement-metric">+15% throughput, -2.3 hours average wait time</span>
        </div>
    </div>
    <div class="warning-actions">
        <button class="btn btn-secondary" onclick="closeDialog()">Cancel</button>
        <button class="btn btn-primary" onclick="confirmOptimization()">Continue Optimization</button>
    </div>
</div>
```

##### Progressive Disclosure
- **Basic Mode**: Simple interface for common operations
- **Advanced Mode**: Full controls for power users
- **Expert Mode**: Direct parameter access and debugging tools

##### Loading States & Progress Feedback
```html
<div class="optimization-progress-card">
    <div class="progress-header">
        <h4>Optimizing Schedule...</h4>
        <span class="estimated-time">Estimated: 2-5 minutes</span>
    </div>
    <div class="progress-steps">
        <div class="step completed">
            <i class="fas fa-check"></i>
            <span>Loading vessels and constraints</span>
        </div>
        <div class="step active">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Running optimization algorithm</span>
        </div>
        <div class="step pending">
            <i class="fas fa-circle"></i>
            <span>Validating results</span>
        </div>
        <div class="step pending">
            <i class="fas fa-circle"></i>
            <span>Updating schedule</span>
        </div>
    </div>
    <div class="current-activity">
        <span id="current-activity-text">Analyzing 12 vessels across 4 jetties...</span>
    </div>
</div>
```

#### 4.6 Interactive Tutorials & Onboarding

##### Feature Discovery
- **Tooltip Tours**: Guided introduction to new locking features
- **Interactive Tutorials**: Step-by-step workflows for common tasks
- **Feature Announcements**: Non-intrusive notifications for new capabilities

##### Contextual Learning
```html
<div class="help-sidebar" id="contextual-help">
    <div class="help-header">
        <h4>Quick Help</h4>
        <button class="help-close" onclick="toggleHelp()">×</button>
    </div>
    <div class="help-content" id="help-content">
        <!-- Dynamic content based on current page/action -->
        <div class="help-section">
            <h5>Assignment Locking</h5>
            <p>Lock assignments to prevent them from being changed during optimization.</p>
            <ul>
                <li><strong>Soft Lock:</strong> Protected but can be overridden</li>
                <li><strong>Hard Lock:</strong> Cannot be modified</li>
                <li><strong>Time Lock:</strong> Time fixed, but vessel/jetty flexible</li>
            </ul>
        </div>
    </div>
</div>
```

#### 4.7 Error Handling & Recovery

##### Graceful Error Messages
```html
<div class="error-message-card">
    <div class="error-header">
        <i class="fas fa-exclamation-circle"></i>
        <h4>Optimization Failed</h4>
    </div>
    <div class="error-details">
        <p class="error-summary">Unable to find feasible schedule with current constraints.</p>
        <div class="error-suggestions">
            <h5>Suggested Solutions:</h5>
            <ul>
                <li>
                    <button class="suggestion-btn" onclick="relaxConstraints()">
                        🔧 Relax time constraints by 2 hours
                    </button>
                </li>
                <li>
                    <button class="suggestion-btn" onclick="unlockSomeAssignments()">
                        🔓 Temporarily unlock flexible assignments
                    </button>
                </li>
                <li>
                    <button class="suggestion-btn" onclick="extendHorizon()">
                        📅 Extend planning horizon to 10 days
                    </button>
                </li>
            </ul>
        </div>
    </div>
    <div class="error-actions">
        <button class="btn btn-secondary" onclick="viewDetails()">View Technical Details</button>
        <button class="btn btn-primary" onclick="retryOptimization()">Try Again</button>
    </div>
</div>
```

##### Recovery Options
- **Automatic Retry**: With adjusted parameters
- **Rollback Capability**: Return to previous schedule state
- **Manual Resolution**: Guided conflict resolution workflow

#### 4.8 Real-time Updates & Notifications

##### Live Status Updates
```javascript
// WebSocket or polling for real-time updates
function updateOptimizationProgress(progress) {
    const statusIcon = document.getElementById('optimization-status-icon');
    const statusText = document.getElementById('optimization-status-text');
    const progressFill = document.getElementById('optimization-progress-fill');
    const progressText = document.getElementById('optimization-progress-text');
    
    statusIcon.className = progress.status === 'running' ? 'fas fa-spinner fa-spin text-primary' : 'fas fa-circle text-success';
    statusText.textContent = progress.message;
    progressFill.style.width = `${progress.percentage}%`;
    progressText.textContent = `${progress.percentage}%`;
    
    // Show current activity
    document.getElementById('current-activity-text').textContent = progress.currentActivity;
}
```

##### Smart Notifications
- **Success Notifications**: With summary of changes and improvements
- **Warning Notifications**: For potential issues or suboptimal results
- **Information Notifications**: For system status and tips

##### Undo/Redo Capability
```html
<div class="action-history-panel">
    <div class="recent-actions">
        <h5>Recent Changes</h5>
        <div class="action-item">
            <span class="action-description">Optimized 5 vessels</span>
            <span class="action-time">2 minutes ago</span>
            <button class="btn btn-sm btn-outline-secondary" onclick="undoAction('opt_001')">
                <i class="fas fa-undo"></i> Undo
            </button>
        </div>
        <div class="action-item">
            <span class="action-description">Locked Assignment #247</span>
            <span class="action-time">5 minutes ago</span>
            <button class="btn btn-sm btn-outline-secondary" onclick="undoAction('lock_247')">
                <i class="fas fa-undo"></i> Undo
            </button>
        </div>
    </div>
</div>
```

#### 4.9 Performance & Accessibility

##### Loading Optimization
- **Skeleton Screens**: Show content structure while loading
- **Lazy Loading**: Load detailed assignment data on demand
- **Caching**: Smart caching of optimization results and vessel data

##### Accessibility Features
- **Keyboard Navigation**: Full keyboard support for all locking operations
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **High Contrast Mode**: Support for accessibility color schemes
- **Font Scaling**: Responsive to user font size preferences

### Phase 5: Advanced Features

#### 5.1 Assignment Templates & Patterns
- **Recurring Assignments**: Templates for regular vessel patterns
- **Seasonal Adjustments**: Preset optimizations for different seasons/periods
- **Customer Priority Profiles**: Saved optimization weights per customer

#### 5.2 Optimization Scheduling
- **Automated Re-optimization**: Scheduled optimization runs (daily, weekly)
- **Trigger-based Optimization**: Auto-optimize when new vessels arrive
- **Smart Notifications**: Alerts for optimization opportunities

#### 5.3 Analytics & Reporting
- **Lock Efficiency**: Metrics on how locks affect optimization quality
- **Conflict Analysis**: Historical data on conflicts and resolutions  
- **Optimization Impact**: Before/after comparisons of schedule changes

## Implementation Priority & Timeline

### Sprint 1 (2 weeks): Core Locking Foundation
- [x] Database schema for assignment locking
- [x] Basic lock/unlock API endpoints  
- [ ] UI lock toggle buttons and status indicators
    - Lock/Unlock buttons added to `schedule.html` (basic handlers)
- [x] Update optimization to respect locked assignments
- [ ] Real-time status indicators with progress bars

Repository-specific checklist:
- [x] Update `src/db/models.py` (`Assignment` lock fields)
- [x] Create Alembic migration for lock fields and upgrade (head: `67518952a705`)
- [x] Add lock/unlock/bulk-lock routes in `src/api/assignment_api.py` and wire in `fastapi_app.py`
- [x] Add lock icons and actions to `src/templates/schedule.html`
- [x] Implement lock honoring in scheduler (via reserved intervals)
- [x] Add `src/static/js/optimization.js` (client helpers added; status panel pending)

### Sprint 2 (2 weeks): Enhanced Optimization Modes
- [x] Enhanced OptimizationParameters model
- [ ] INCREMENTAL and SELECTIVE optimization modes
- [x] Vessel and time window filtering
- [x] UI mode selection with dynamic explanations
- [x] Smart warning dialogs with impact preview

Repository-specific checklist:
- [x] Add selective/time-window endpoints (implemented in `src/api/fastapi_app.py`)
- [x] Extend scheduler with `vessel_filter`, `time_window_*` (optimization_mode TBD)
- [x] Wire `optimization.js`; mode selector UI added (basic), help text and start button
- [x] Add `/api/optimize/preview` endpoint and impact preview modal

### Sprint 3 (2 weeks): Conflict Management & User Feedback

Repository-specific checklist:
- [ ] Add `src/optimization/conflicts.py` with `ConflictDetector`
- [ ] Integrate conflict summaries into optimization response
- [ ] Render conflict panel in `schedule.html` and update via `optimization.js`
- [ ] Conflict detection algorithms
- [ ] Resolution suggestion engine
- [ ] Conflict UI panels and workflows
- [ ] Lock override mechanisms
- [ ] Graceful error handling with recovery suggestions
- [ ] Interactive help system with contextual content

### Sprint 4 (2 weeks): Advanced UI & User Experience

Repository-specific checklist:
- [ ] Bulk selection UI in `schedule.html`
- [ ] Preview/simulation pathway in `optimization_api.py`
- [ ] Undo/redo history persisted minimally in `optimization_sessions` or client-side state
- [ ] Bulk operations for assignments
- [ ] Advanced filtering and selection
- [ ] Optimization preview/simulation
- [ ] Progressive disclosure (Basic/Advanced/Expert modes)
- [ ] Step-by-step progress indicators
- [ ] Undo/redo capability with action history
- [ ] Loading states and skeleton screens

### Sprint 5 (1 week): Interactive Learning & Accessibility

Repository-specific checklist:
- [ ] Tooltip tours and contextual help blocks in `schedule.html`
- [ ] Keyboard navigation tests for new UI elements
- [ ] Interactive tutorials and tooltip tours
- [ ] Feature announcements and onboarding
- [ ] Full keyboard navigation and screen reader support
- [ ] Live updates via WebSocket/polling
- [ ] Smart notification system

### Sprint 6 (1 week): Polish & Documentation
- [ ] User documentation and help system
- [ ] Performance optimization and caching
- [ ] Comprehensive error handling
- [ ] Testing and validation
- [ ] Accessibility compliance verification

Repository-specific checklist:
- [ ] Write developer docs in `docs/` for locking and selective optimization
- [ ] Add API examples and curl/PowerShell snippets

## Success Metrics

### Functional Metrics
- [ ] All 6 optimization scenarios work as specified
- [ ] Zero data loss during optimization operations
- [ ] Locked assignments remain unchanged unless explicitly overridden
- [ ] Conflict detection catches 100% of time/resource conflicts
- [ ] Error recovery suggestions resolve ≥ 85% of common optimization failures

### Performance Metrics  
- [ ] Optimization with locked assignments ≤ 2x slower than fresh optimization
- [ ] UI lock operations complete in ≤ 500ms
- [ ] Selective optimization on 50% of vessels ≤ 60% of full optimization time
- [ ] Real-time progress updates ≤ 2 second latency
- [ ] Page load times ≤ 3 seconds with skeleton screens

### User Experience Metrics
- [ ] Users can complete common locking workflows in ≤ 3 clicks
- [ ] Conflict resolution suggestions are relevant in ≥ 80% of cases
- [ ] Training time for new optimization features ≤ 30 minutes
- [ ] ≥ 90% of users understand optimization mode impacts before proceeding
- [ ] Error messages lead to successful resolution in ≥ 75% of cases
- [ ] Users find contextual help relevant in ≥ 85% of interactions

### Responsiveness & Feedback Metrics
- [ ] All optimization operations provide real-time progress feedback
- [ ] Users receive confirmation within 2 seconds of any action
- [ ] Error states include actionable recovery steps in 100% of cases
- [ ] Loading states are shown for all operations ≥ 1 second
- [ ] Success notifications include meaningful impact summaries

### Accessibility & Usability Metrics
- [ ] Full keyboard navigation for all optimization features
- [ ] Screen reader compatibility with proper ARIA labels
- [ ] Interactive tutorials completed successfully by ≥ 80% of new users
- [ ] Feature discovery time ≤ 5 minutes for core locking functionality
- [ ] Help system provides answers within ≤ 30 seconds of query

## Risk Mitigation

### Technical Risks
- **Complex Constraint Logic**: Start with simple lock types, add complexity gradually
- **Performance Degradation**: Implement optimization caching and incremental updates
- **Data Consistency**: Add comprehensive validation and rollback mechanisms
- **Real-time Update Failures**: Graceful degradation to polling, clear offline indicators

### User Experience Risks  
- **Feature Complexity**: Provide guided workflows and smart defaults
- **Learning Curve**: Create interactive tutorials and contextual help
- **Change Management**: Gradual rollout with feature flags and user feedback
- **Information Overload**: Progressive disclosure with Basic/Advanced/Expert modes
- **Unclear Feedback**: Always show loading states, progress, and confirmation messages
- **Error Frustration**: Provide clear error messages with actionable recovery steps

### UI/UX Specific Risks
- **User Confusion**: Implement consistent visual language and interaction patterns
- **Feature Discovery**: Use tooltip tours and feature announcements for new capabilities
- **Accessibility Barriers**: Follow WCAG guidelines, test with screen readers and keyboard-only users
- **Mobile Responsiveness**: Ensure all new features work on smaller screens
- **Cognitive Load**: Limit choices per screen, use smart defaults and auto-suggestions

### Business Risks
- **Schedule Disruption**: Extensive testing in staging environments
- **Customer Impact**: Emergency rollback procedures and manual override capabilities
- **Operational Overhead**: Automation and smart defaults to minimize manual work
- **User Adoption**: Comprehensive training materials and gradual feature introduction
- **Support Burden**: Self-service help system and automated troubleshooting

## Future Enhancements

### Phase 6+: Advanced Intelligence
- **ML-Driven Lock Suggestions**: AI recommendations for which assignments to lock
- **Predictive Conflict Resolution**: Proactive identification of potential conflicts  
- **Dynamic Optimization**: Real-time re-optimization based on changing conditions
- **Integration APIs**: Connect with external planning and ERP systems
