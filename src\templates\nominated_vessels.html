{% extends "base.html" %}

{% block title %}Nominated Vessels - Terneuzen Terminal ETA Tracking{% endblock %}

{% block header %}<span id="terminal-nominated-title">Nominated Vessels - Smart ETA Tracking</span>{% endblock %}

{% block user_actions %}
    <button class="btn btn-secondary" id="refresh-btn">
        <i class="fas fa-sync-alt"></i>
        Refresh ETAs
    </button>
    <button class="btn btn-info" id="toggle-ais-status">
        <i class="fas fa-satellite-dish"></i>
        AIS Status
    </button>
    <button class="btn btn-warning" id="reset-data-btn">
        <i class="fas fa-undo"></i>
        Reset All Data
    </button>
    <a href="/nomination" class="btn btn-primary">
        <i class="fas fa-plus"></i>
        Add Nomination
    </a>
{% endblock %}

{% block content %}
            <!-- AIS & Services Status Panel -->
            <div class="card" id="services-status-card">
                <div class="card-header">
                    <h3><i class="fas fa-satellite-dish"></i> Services Status</h3>
                </div>
                <div class="card-body">
                    <div class="services-status-grid">
                        <div class="service-status" id="ais-status">
                            <div class="status-icon">
                                <i class="fas fa-satellite-dish"></i>
                            </div>
                            <div class="status-info">
                                <h4>AIS Stream</h4>
                                <div class="status-text">Loading...</div>
                                <div class="status-detail">Vessel positions</div>
                            </div>
                        </div>
                        <div class="service-status" id="tidal-status">
                            <div class="status-icon">
                                <i class="fas fa-water"></i>
                            </div>
                            <div class="status-info">
                                <h4>Tidal Data</h4>
                                <div class="status-text">Loading...</div>
                                <div class="status-detail">RWS Waterwebservices</div>
                            </div>
                        </div>
                        <div class="service-status" id="locks-status">
                            <div class="status-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="status-info">
                                <h4>Lock Status</h4>
                                <div class="status-text">Loading...</div>
                                <div class="status-detail">RWS & BGV APIs</div>
                            </div>
                        </div>
                        <div class="service-status" id="tracking-status">
                            <div class="status-icon">
                                <i class="fas fa-route"></i>
                            </div>
                            <div class="status-info">
                                <h4>Ship Tracking</h4>
                                <div class="status-text">Loading...</div>
                                <div class="status-detail">ETA calculations</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Nominated Vessels Filter -->
            <div class="filters">
                <div class="filter-group">
                    <label for="eta-filter">ETA Range:</label>
                    <select id="eta-filter">
                        <option value="">All</option>
                        <option value="next_6h">Next 6 Hours</option>
                        <option value="next_12h">Next 12 Hours</option>
                        <option value="next_24h">Next 24 Hours</option>
                        <option value="next_48h">Next 48 Hours</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="geofence-filter">Proximity:</label>
                    <select id="geofence-filter">
                        <option value="">All</option>
                        <option value="terminal">At Terminal</option>
                        <option value="2hour">2-Hour Zone</option>
                        <option value="4hour">4-Hour Zone</option>
                        <option value="tracked">Being Tracked</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="status-filter">Status:</label>
                    <select id="status-filter">
                        <option value="">All</option>
                        <option value="EN_ROUTE">EN_ROUTE</option>
                        <option value="APPROACHING">APPROACHING</option>
                        <option value="ARRIVED">ARRIVED</option>
                        <option value="WAITING">WAITING</option>
                    </select>
                </div>
                <button id="apply-filters" class="btn btn-sm btn-primary">Apply Filters</button>
            </div>

            <!-- AIS Map -->
            <div class="card">
                <div class="card-header">
                    <h3>Live AIS Map</h3>
                </div>
                <div class="card-body">
                    <div id="ais-map" style="height: 420px; border-radius: 8px; overflow: hidden;"></div>
                </div>
            </div>

            <!-- Nominated Vessels List -->
            <div class="card">
                <div class="card-header">
                    <h3>Nominated Vessels - Smart ETA Tracking</h3>
                    <div class="header-actions">
                        <span id="vessel-count-badge" class="badge">0 vessels</span>
                        <span id="last-update-time" class="text-muted">Last updated: Never</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Vessel</th>
                                    <th>Current Position</th>
                                    <th>Distance</th>
                                    <th>Original ETA</th>
                                    <th>Smart ETA</th>
                                    <th>Tidal Factor</th>
                                    <th>Lock Status</th>
                                    <th>Geofence</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="nominated-vessels-tbody">
                                <tr>
                                    <td colspan="10" class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i> Loading nominated vessels...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Selected Vessel ETA Details -->
            <div class="card" id="vessel-eta-details-card" style="display: none;">
                <div class="card-header">
                    <h3>ETA Analysis: <span id="detail-vessel-name"></span></h3>
                </div>
                <div class="card-body">
                    <div class="eta-analysis-grid">
                        <!-- Position & Movement -->
                        <div class="analysis-section">
                            <h4><i class="fas fa-map-marker-alt"></i> Position & Movement</h4>
                            <div class="detail-row">
                                <div class="detail-label">Current Position:</div>
                                <div class="detail-value" id="detail-position"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Speed:</div>
                                <div class="detail-value" id="detail-speed"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Course:</div>
                                <div class="detail-value" id="detail-course"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Distance to Terminal:</div>
                                <div class="detail-value" id="detail-distance"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">AIS Last Update:</div>
                                <div class="detail-value" id="detail-last-update"></div>
                            </div>
                        </div>

                        <!-- ETA Calculations -->
                        <div class="analysis-section">
                            <h4><i class="fas fa-clock"></i> ETA Breakdown</h4>
                            <div class="detail-row">
                                <div class="detail-label">Original ETA:</div>
                                <div class="detail-value" id="detail-original-eta"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Base Travel Time:</div>
                                <div class="detail-value" id="detail-base-time"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Tidal Delay:</div>
                                <div class="detail-value" id="detail-tidal-delay"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Lock Wait Time:</div>
                                <div class="detail-value" id="detail-lock-delay"></div>
                            </div>
                            <div class="detail-row highlight">
                                <div class="detail-label">Smart ETA:</div>
                                <div class="detail-value" id="detail-smart-eta"></div>
                            </div>
                        </div>

                        <!-- Tidal Information -->
                        <div class="analysis-section">
                            <h4><i class="fas fa-water"></i> Tidal Conditions</h4>
                            <div class="detail-row">
                                <div class="detail-label">Current Water Level:</div>
                                <div class="detail-value" id="detail-water-level"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Vessel Draft:</div>
                                <div class="detail-value" id="detail-vessel-draft"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Navigation Status:</div>
                                <div class="detail-value" id="detail-navigation-safe"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Next High Tide:</div>
                                <div class="detail-value" id="detail-next-high-tide"></div>
                            </div>
                        </div>

                        <!-- Lock Information -->
                        <div class="analysis-section">
                            <h4><i class="fas fa-lock"></i> Lock Status</h4>
                            <div id="lock-details-container">
                                <!-- Lock details will be populated here -->
                            </div>
                        </div>
                    </div>

                    <div class="eta-actions">
                        <button class="btn btn-primary" id="track-vessel-btn">
                            <i class="fas fa-crosshairs"></i> Start Tracking
                        </button>
                        <button class="btn btn-info" id="view-tracking-btn">
                            <i class="fas fa-route"></i> View on Tracking Dashboard
                        </button>
                        <button class="btn btn-secondary" id="refresh-eta-btn">
                            <i class="fas fa-sync-alt"></i> Refresh ETA
                        </button>
                    </div>
                </div>
            </div>

            <!-- ETA Legend -->
            <div class="status-legend-container">
                <div class="status-legend-toggle">
                    <i class="fas fa-info-circle"></i> ETA Calculation Guide
                </div>
                <div class="status-legend">
                    <div class="legend-section">
                        <h4>Smart ETA Factors</h4>
                        <div class="legend-items">
                            <div class="legend-item">
                                <i class="fas fa-map-marker-alt text-info"></i>
                                <span class="legend-description"><strong>Real-time Position:</strong> Live AIS tracking with speed and course</span>
                            </div>
                            <div class="legend-item">
                                <i class="fas fa-water text-blue"></i>
                                <span class="legend-description"><strong>Tidal Conditions:</strong> Current water levels and safe navigation windows</span>
                            </div>
                            <div class="legend-item">
                                <i class="fas fa-lock text-warning"></i>
                                <span class="legend-description"><strong>Lock Status:</strong> Operational status and wait times at locks</span>
                            </div>
                            <div class="legend-item">
                                <i class="fas fa-route text-success"></i>
                                <span class="legend-description"><strong>Distance Calculation:</strong> Waterway distance with speed optimization</span>
                            </div>
                        </div>
                    </div>
                    <div class="legend-section">
                        <h4>Geofence Zones</h4>
                        <div class="legend-items">
                            <div class="legend-item">
                                <span class="geofence-badge terminal">Terminal</span>
                                <span class="legend-description">At terminal berth</span>
                            </div>
                            <div class="legend-item">
                                <span class="geofence-badge zone-2h">2-Hour</span>
                                <span class="legend-description">Within 2-hour approach zone</span>
                            </div>
                            <div class="legend-item">
                                <span class="geofence-badge zone-4h">4-Hour</span>
                                <span class="legend-description">Within 4-hour approach zone</span>
                            </div>
                            <div class="legend-item">
                                <span class="geofence-badge tracked">Tracked</span>
                                <span class="legend-description">Being tracked by AIS</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

    <style>
        /* Services Status Grid */
        .services-status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        /* Filters - modern layout and selects */
        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 0.75rem;
            align-items: end;
            margin: 0 0 1rem 0;
        }

        .filters .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filters label {
            font-size: 0.8rem;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 0.35rem;
        }

        .filters select {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 100%;
            padding: 10px 36px 10px 12px;
            font-size: 0.95rem;
            line-height: 1.2;
            color: #111827;
            background-color: #fff;
            border: 1px solid #dfe3ea;
            border-radius: 10px;
            box-shadow: 0 1px 2px rgba(16,24,40,0.05);
            transition: border-color .2s ease, box-shadow .2s ease;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='none' stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' viewBox='0 0 24 24'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
        }

        .filters select:focus {
            outline: none;
            border-color: #0d6efd;
            box-shadow: 0 0 0 4px rgba(13,110,253,0.15);
        }

        .filters #apply-filters.btn {
            height: 42px;
            border-radius: 10px;
            font-weight: 600;
            padding: 0 16px;
        }

        .service-status {
            display: flex;
            align-items: center;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .service-status.connected {
            border-color: #28a745;
            background: #d4edda;
        }

        .service-status.warning {
            border-color: #ffc107;
            background: #fff3cd;
        }

        .service-status.error {
            border-color: #dc3545;
            background: #f8d7da;
        }

        .status-icon {
            font-size: 1.5rem;
            margin-right: 1rem;
            color: #6c757d;
        }

        .service-status.connected .status-icon {
            color: #28a745;
        }

        .service-status.warning .status-icon {
            color: #ffc107;
        }

        .service-status.error .status-icon {
            color: #dc3545;
        }

        .status-info h4 {
            margin: 0 0 0.25rem 0;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status-text {
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-detail {
            font-size: 0.7rem;
            color: #6c757d;
        }

        /* Header Actions */
        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .badge {
            background: #007bff;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.8rem;
        }

        /* ETA Analysis Grid */
        .eta-analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .analysis-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
        }

        .analysis-section h4 {
            margin: 0 0 1rem 0;
            color: #495057;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
        }

        .detail-row.highlight {
            background: #e7f3ff;
            padding: 0.5rem;
            border-radius: 4px;
            font-weight: 600;
        }

        /* Geofence badges */
        .geofence-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .geofence-badge.terminal {
            background: #d4edda;
            color: #155724;
        }

        .geofence-badge.zone-2h {
            background: #fff3cd;
            color: #856404;
        }

        .geofence-badge.zone-4h {
            background: #cce5ff;
            color: #004085;
        }

        .geofence-badge.tracked {
            background: #e2e3e5;
            color: #383d41;
        }

        /* ETA comparison styles */
        .eta-comparison {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .eta-original {
            color: #6c757d;
            text-decoration: line-through;
        }

        .eta-smart {
            font-weight: 600;
            color: #007bff;
        }

        .eta-change {
            font-size: 0.8rem;
            padding: 0.1rem 0.3rem;
            border-radius: 0.2rem;
        }

        .eta-change.earlier {
            background: #d4edda;
            color: #155724;
        }

        .eta-change.later {
            background: #f8d7da;
            color: #721c24;
        }

        /* Tidal and lock status indicators */
        .tidal-status, .lock-status {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.8rem;
        }

        .tidal-status.safe {
            color: #28a745;
        }

        .tidal-status.warning {
            color: #ffc107;
        }

        .tidal-status.unsafe {
            color: #dc3545;
        }

        .lock-status.operational {
            color: #28a745;
        }

        .lock-status.maintenance {
            color: #ffc107;
        }

        .lock-status.closed {
            color: #dc3545;
        }

        /* Position display */
        .position-display {
            font-family: monospace;
            font-size: 0.9rem;
        }

        /* Distance display */
        .distance-display {
            font-weight: 600;
        }

        /* ETA actions */
        .eta-actions {
            display: flex;
            gap: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .services-status-grid {
                grid-template-columns: 1fr;
            }
            
            .eta-analysis-grid {
                grid-template-columns: 1fr;
            }
            
            .eta-actions {
                flex-direction: column;
            }
        }
    </style>

    <!-- Leaflet for map rendering -->
    <link rel="stylesheet" href="/static/vendor/css/leaflet-1.9.4.min.css"/>
    <script src="/static/vendor/js/leaflet-1.9.4.min.js"></script>
    <script src="/static/js/ship-map.js" nonce="{{ nonce }}"></script>
    <script src="/static/js/nominated-vessels.js?v=20250909-eta-fix" nonce="{{ nonce }}"></script>
    <script nonce="{{ nonce }}">
        document.addEventListener('DOMContentLoaded', async () => {
            // Initialize map only once
            if (window.ShipMap && !window.smartEtaMap) {
                window.smartEtaMap = new ShipMap('ais-map', {
                    onShipClick: (ship) => {
                        console.log('Ship clicked:', ship);
                    }
                });
            }

            // Setup a single polling loop for ships
            const loadShipsToMap = async () => {
                try {
                    const res = await fetch('/api/tracking/ships');
                    if (res.ok) {
                        const data = await res.json();
                        if (window.smartEtaMap) {
                            window.smartEtaMap.updateShips(data.ships || []);
                            // Highlight nominated vessels by MMSI
                            try {
                                const vesselsRes = await fetch('/api/vessels?status=EN_ROUTE&status=APPROACHING&status=ARRIVED&status=WAITING');
                                if (vesselsRes.ok) {
                                    const vessels = await vesselsRes.json();
                                    const nominatedMmsi = vessels
                                        .map(v => v.mmsi)
                                        .filter(Boolean)
                                        .map(String);
                                    window.smartEtaMap.setNominated(nominatedMmsi);
                                }
                            } catch (e) { /* ignore */ }
                            // Load key locks and show on map
                            try {
                                const lockCodes = ['TNZN', 'HANS', 'KREK'];
                                const lockPromises = lockCodes.map(code => fetch(`/api/locks/status/${code}`));
                                const responses = await Promise.allSettled(lockPromises);
                                const locks = [];
                                for (const r of responses) {
                                    if (r.status === 'fulfilled' && r.value.ok) {
                                        const d = await r.value.json();
                                        locks.push(d);
                                    }
                                }
                                if (locks.length && window.smartEtaMap.updateLocks) {
                                    window.smartEtaMap.updateLocks(locks);
                                }
                            } catch (e) { /* ignore */ }
                        }
                    }
                } catch (e) { console.warn('Failed loading ships for map', e); }
            };

            // Ensure we don't create multiple intervals
            if (window.smartEtaInterval) clearInterval(window.smartEtaInterval);
            await loadShipsToMap();
            window.smartEtaInterval = setInterval(loadShipsToMap, 60000);

            // Clean up on unload
            window.addEventListener('beforeunload', () => {
                if (window.smartEtaInterval) clearInterval(window.smartEtaInterval);
            });

            // Reset data functionality
            document.getElementById('reset-data-btn').addEventListener('click', async function() {
                if (!confirm('Are you sure you want to reset ALL data? This will:\n\n• Delete all assignments\n• Delete all vessels\n• Reset all settings to defaults\n\nThis action cannot be undone.')) {
                    return;
                }

                const btn = this;
                const originalHtml = btn.innerHTML;
                try {
                    btn.disabled = true;
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';

                    // Call reset API endpoint
                    const response = await fetch('/api/reset-data', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        const error = await response.text();
                        throw new Error(error || 'Failed to reset data');
                    }

                    const result = await response.json();

                    if (result.success) {
                        alert('All data has been reset successfully. The page will reload.');
                        window.location.reload();
                    } else {
                        alert('Reset failed: ' + (result.message || 'Unknown error'));
                    }

                } catch (error) {
                    console.error('Reset failed:', error);
                    alert('Failed to reset data: ' + (error.message || error));
                } finally {
                    btn.disabled = false;
                    btn.innerHTML = originalHtml;
                }
            });
        });
    </script>

{% endblock %}
