services:
  jetty-planning-app:
    build: .
    container_name: jetty-planning-app
    env_file:
      - .env
    ports:
      - "7000:7000"
    environment:
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-TEST}
      - API_HOST=0.0.0.0
      - API_PORT=7000
      - PRODUCTION=true
      # AIS Stream configuration
      - AISSTREAM_API_KEY=${AISSTREAM_API_KEY}
      - AIS_PRELOAD=${AIS_PRELOAD}
      - AIS_RADIUS_KM=${AIS_RADIUS_KM}
      # PostgreSQL Database Configuration
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=${DB_NAME:-planner}
      - DB_USER=${DB_USER:-postgres}
      - DB_PASSWORD=${DB_PASSWORD}
      - DATABASE_URL=${DATABASE_URL:-postgresql+psycopg://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}}
    volumes:
      # Optional volume for persistent data
      - jetty-data:/app/data
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - jetty-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7000/api/terminal"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  postgres:
    image: postgres:16-alpine
    container_name: jetty-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${DB_NAME:-planner}
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-devpassword}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8
    ports:
      - "7432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      # Optional: Initialize with schema
      - ./docker/postgres/initdb:/docker-entrypoint-initdb.d:ro
    networks:
      - jetty-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-planner}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # PostgreSQL Database Administration (pgAdmin)
  pgadmin:
    image: dpage/pgadmin4:9.8
    container_name: jetty-pgadmin
    restart: unless-stopped
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_EMAIL:-<EMAIL>}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_PASSWORD:-admin123}
      - PGADMIN_CONFIG_SERVER_MODE=False
      - PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED=False
    ports:
      - "8800:80"
    volumes:
      - pgadmin-data:/var/lib/pgadmin
      - ./scripts/servers.json:/pgadmin4/servers.json:ro
    networks:
      - jetty-network
    depends_on:
      postgres:
        condition: service_healthy

  # Automated PostgreSQL Backup Service
  postgres-backup:
    image: prodrigestivill/postgres-backup-local:16
    container_name: jetty-postgres-backup
    restart: unless-stopped
    environment:
      # Database connection
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=${DB_NAME:-planner}
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      
      # Backup schedule (daily at 2 AM)
      - SCHEDULE=0 2 * * *
      
      # Backup retention
      - BACKUP_KEEP_DAYS=30
      - BACKUP_KEEP_WEEKS=8
      - BACKUP_KEEP_MONTHS=6
      
      # Backup naming and compression
      - BACKUP_SUFFIX=.sql
      - POSTGRES_EXTRA_OPTS=-Z6 --schema=public --blobs
      
      # Health check settings
      - HEALTHCHECK_PORT=8080
    volumes:
      # Local backup storage
      - ./backups:/backups
      # Timezone for scheduled backups
      - /etc/localtime:/etc/localtime:ro
    networks:
      - jetty-network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backup monitoring and web interface
  backup-web:
    image: nginx:alpine
    container_name: jetty-backup-web
    restart: unless-stopped
    ports:
      - "8081:80"
    volumes:
      - ./backups:/usr/share/nginx/html/backups:ro
      - ./scripts/backup-web.conf:/etc/nginx/nginx.conf:ro
    networks:
      - jetty-network
    depends_on:
      - postgres-backup

networks:
  jetty-network:
    driver: bridge

volumes:
  jetty-data:
  postgres-data:
    driver: local
  pgadmin-data:
    driver: local
