# Vessel Registry & Visit Tracking Implementation Plan

## Overview
Implement a persistent vessel registry system that properly tracks vessel identity across multiple visits and integrates with AIS data. This will solve the current issue where recurring vessel visits create separate vessel identities instead of being linked to the same physical ship.

## Current State Analysis

### Problems with Current System
1. **No Vessel Identity Persistence**: Each nomination creates a new vessel ID (NV001, NV002, etc.)
2. **Assignment Confusion**: Cannot distinguish visits by the same vessel
3. **AIS Integration Gap**: AIS data (MMSI/IMO) not properly linked to vessel records
4. **Historical Data Loss**: No way to track vessel performance over multiple visits
5. **Inconsistent Identity**: Same vessel might have different names/details across visits

### Current Data Flow
```
AIS Data → Nomination Form → In-Memory Vessel (NV001) → Assignment → Database
```

## Target Architecture

### New Data Flow
```
AIS Data → Vessel Registry Lookup/Create → Vessel Visit → Nomination → Assignment → Database
              ↓                           ↓               ↓           ↓
          Persistent ID              Visit Counter    NV001 (temp)  Links to Vessel
```

## Database Schema Changes

### 1. Enhanced Vessel Master Registry
```sql
CREATE TABLE vessels (
    id SERIAL PRIMARY KEY,
    
    -- Unique Identifiers (IMO is the gold standard)
    imo VARCHAR(10) UNIQUE,           -- International Maritime Organization number
    mmsi VARCHAR(9) UNIQUE,           -- Maritime Mobile Service Identity (AIS)
    call_sign VARCHAR(10),            -- Radio call sign
    
    -- Vessel Identity
    name VARCHAR(255) NOT NULL,       -- Official vessel name
    previous_names TEXT[],            -- Array of historical names
    vessel_type VARCHAR(50) NOT NULL, -- TANKER, BARGE, CONTAINER, etc.
    vessel_subtype VARCHAR(50),       -- OIL_TANKER, CHEMICAL_TANKER, etc.
    
    -- Physical Characteristics
    deadweight DECIMAL(10,2),         -- DWT in tonnes
    gross_tonnage DECIMAL(10,2),      -- GT
    length_overall DECIMAL(6,2),      -- LOA in meters
    beam DECIMAL(5,2),                -- Beam in meters
    maximum_draft DECIMAL(4,2),       -- Max draft in meters
    
    -- Registration & Ownership
    flag_state VARCHAR(3),            -- ISO country code
    port_of_registry VARCHAR(100),
    owner VARCHAR(255),
    operator VARCHAR(255),
    manager VARCHAR(255),
    
    -- Build Information
    build_year INTEGER,
    shipyard VARCHAR(255),
    hull_number VARCHAR(50),
    
    -- Operational Status
    status VARCHAR(20) DEFAULT 'ACTIVE', -- ACTIVE, SCRAPPED, LOST, etc.
    is_blacklisted BOOLEAN DEFAULT FALSE,
    
    -- Data Sources & Confidence
    data_source VARCHAR(50),          -- AIS, MANUAL, API, etc.
    confidence_score INTEGER DEFAULT 100, -- 1-100, data quality score
    last_ais_update TIMESTAMP,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by VARCHAR(100),
    notes TEXT
);

-- Indexes for fast lookups
CREATE INDEX idx_vessels_imo ON vessels(imo);
CREATE INDEX idx_vessels_mmsi ON vessels(mmsi);
CREATE INDEX idx_vessels_name ON vessels(name);
```

### 2. Vessel Visits Tracking
```sql
CREATE TABLE vessel_visits (
    id SERIAL PRIMARY KEY,
    vessel_id INTEGER REFERENCES vessels(id) ON DELETE CASCADE,
    terminal_id VARCHAR(50) REFERENCES terminals(id),
    
    -- Visit Identification
    visit_number INTEGER,             -- Auto-increment per vessel
    external_reference VARCHAR(100),  -- Customer/agent reference
    
    -- Visit Timeline
    estimated_arrival TIMESTAMP,
    actual_arrival TIMESTAMP,
    estimated_departure TIMESTAMP,
    actual_departure TIMESTAMP,
    
    -- Visit Purpose
    operation_type VARCHAR(20),       -- LOADING, DISCHARGE, BUNKERING, etc.
    cargo_types TEXT[],              -- Array of cargo products
    total_cargo_volume DECIMAL(10,2),
    
    -- Visit Status
    status VARCHAR(20) DEFAULT 'PLANNED', -- PLANNED, APPROACHING, DOCKED, COMPLETED, CANCELLED
    berth_assignments TEXT[],        -- Array of berth/jetty names used
    
    -- Business Information
    customer VARCHAR(255),
    agent VARCHAR(255),
    priority INTEGER DEFAULT 1,
    
    -- Performance Metrics (filled after completion)
    actual_berth_time INTERVAL,      -- Time from arrival to first berth
    actual_operation_time INTERVAL,  -- Time spent on operations
    total_port_time INTERVAL,        -- Total time in port
    
    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    notes TEXT
);

-- Automatic visit numbering per vessel
CREATE SEQUENCE vessel_visit_numbers;
CREATE OR REPLACE FUNCTION assign_visit_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.visit_number IS NULL THEN
        SELECT COALESCE(MAX(visit_number), 0) + 1 
        INTO NEW.visit_number
        FROM vessel_visits 
        WHERE vessel_id = NEW.vessel_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER assign_visit_number_trigger
    BEFORE INSERT ON vessel_visits
    FOR EACH ROW
    EXECUTE FUNCTION assign_visit_number();
```

### 3. Enhanced Assignments Table
```sql
-- Add new columns to existing assignments table
ALTER TABLE assignments 
ADD COLUMN vessel_db_id INTEGER REFERENCES vessels(id),
ADD COLUMN visit_id INTEGER REFERENCES vessel_visits(id),
ADD COLUMN nomination_reference VARCHAR(50),
ADD COLUMN assignment_type VARCHAR(20) DEFAULT 'SCHEDULED'; -- SCHEDULED, EMERGENCY, MAINTENANCE

-- Create index for vessel lookups
CREATE INDEX idx_assignments_vessel_db_id ON assignments(vessel_db_id);
CREATE INDEX idx_assignments_visit_id ON assignments(visit_id);
```

### 4. AIS Integration Table
```sql
CREATE TABLE vessel_ais_data (
    id SERIAL PRIMARY KEY,
    vessel_id INTEGER REFERENCES vessels(id) ON DELETE CASCADE,
    
    -- AIS Position Data
    mmsi VARCHAR(9) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    course DECIMAL(5, 2),            -- Course over ground
    speed DECIMAL(5, 2),             -- Speed over ground in knots
    heading INTEGER,                 -- True heading
    
    -- Vessel Status from AIS
    navigation_status INTEGER,       -- AIS navigation status code
    rate_of_turn INTEGER,
    position_accuracy INTEGER,
    
    -- Dynamic Data
    draught DECIMAL(4, 2),          -- Current draught in meters
    destination VARCHAR(100),        -- Destination from AIS
    eta_raw INTEGER,                -- Raw ETA from AIS
    eta_parsed TIMESTAMP,           -- Parsed ETA
    
    -- Data Quality
    timestamp TIMESTAMP NOT NULL,
    age_seconds INTEGER,            -- How old this data is
    signal_quality INTEGER,         -- Signal strength/quality indicator
    
    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    source VARCHAR(50)              -- AIS provider/source
);

-- Indexes for spatial and temporal queries
CREATE INDEX idx_ais_vessel_id ON vessel_ais_data(vessel_id);
CREATE INDEX idx_ais_timestamp ON vessel_ais_data(timestamp);
CREATE INDEX idx_ais_mmsi ON vessel_ais_data(mmsi);
CREATE INDEX idx_ais_position ON vessel_ais_data(latitude, longitude);
```

## Implementation Phases

### Phase 1: Database Schema & Core Models (Week 1)
1. **Create Database Migrations**
   - Add new tables (vessels, vessel_visits, vessel_ais_data)
   - Modify assignments table
   - Add indexes and constraints

2. **Update ORM Models**
   - Create SQLAlchemy models for new tables
   - Add relationships between models
   - Update existing Assignment model

3. **Database Adapter Updates**
   - Add vessel registry methods
   - Add visit tracking methods
   - Update assignment creation methods

### Phase 2: Vessel Registry Service (Week 2)
1. **Vessel Lookup & Creation Service**
   ```python
   class VesselRegistryService:
       def find_or_create_vessel(self, imo=None, mmsi=None, name=None, **vessel_data)
       def update_vessel_from_ais(self, vessel_id, ais_data)
       def merge_vessel_records(self, primary_id, duplicate_id)
       def get_vessel_history(self, vessel_id)
   ```

2. **AIS Integration Service**
   ```python
   class AISIntegrationService:
       def process_ais_updates(self, ais_data_batch)
       def link_ais_to_vessel(self, mmsi, vessel_id)
       def update_vessel_position(self, vessel_id, position_data)
       def detect_arrival_departure(self, vessel_id, terminal_geofence)
   ```

3. **Visit Management Service**
   ```python
   class VisitTrackingService:
       def create_visit(self, vessel_id, terminal_id, **visit_data)
       def update_visit_status(self, visit_id, status)
       def record_arrival_departure(self, visit_id, timestamp, event_type)
       def calculate_visit_metrics(self, visit_id)
   ```

### Phase 3: Nomination Workflow Integration (Week 3)
1. **Enhanced Nomination Process**
   - Vessel lookup by IMO/MMSI/Name before creating nomination
   - Create or update vessel visit record
   - Link assignment to vessel and visit

2. **AIS Pre-population**
   - Auto-populate nomination form from AIS data
   - Vessel dimension and characteristic lookup
   - Historical performance suggestions

3. **Duplicate Detection**
   - Prevent duplicate nominations for same vessel/visit
   - Merge similar nominations
   - Alert on conflicting schedules

### Phase 4: API & Frontend Updates (Week 4)
1. **Enhanced APIs**
   - `/api/vessels` - Master vessel registry
   - `/api/vessels/{id}/visits` - Visit history
   - `/api/vessels/{id}/performance` - Performance analytics
   - `/api/visits` - Current and planned visits

2. **Frontend Integration**
   - Vessel search with registry data
   - Visit history display
   - Performance metrics dashboard
   - Duplicate warning alerts

### Phase 5: Migration & Data Cleanup (Week 5)
1. **Data Migration Script**
   - Extract unique vessels from existing assignments
   - Create vessel records with best-available data
   - Link historical assignments to vessels
   - Preserve existing functionality during migration

2. **Data Quality Improvements**
   - Merge duplicate vessel records
   - Standardize vessel names and identifiers
   - Validate data against external sources

## AIS Integration Strategy

### Data Flow
```
AIS Stream → AIS Processor → Vessel Registry → Visit Tracker → Notification System
```

### Key Integration Points
1. **Vessel Identification**
   - Use MMSI as primary AIS identifier
   - Cross-reference with IMO when available
   - Fall back to name matching with fuzzy logic

2. **Position Tracking**
   - Store latest position for each vessel
   - Detect terminal approach/departure events
   - Calculate ETAs based on position and speed

3. **Status Updates**
   - Update visit status based on AIS navigation status
   - Detect anchoring, docking, underway events
   - Auto-update arrival/departure times

### Geofencing Logic
```python
# Example geofence detection
def check_terminal_proximity(vessel_position, terminal_position, radius_km=5):
    distance = calculate_distance(vessel_position, terminal_position)
    if distance <= radius_km:
        return "APPROACHING"
    elif distance <= 0.5:  # Very close
        return "DOCKED"
    else:
        return "DISTANT"
```

## Data Quality & Validation

### Vessel Identity Validation
1. **IMO Number Validation**
   - Check IMO number format and checksum
   - Validate against Lloyd's List or similar database
   - Flag suspicious or invalid IMO numbers

2. **MMSI Validation**
   - Validate MMSI format and country code
   - Check for reused or invalid MMSIs
   - Cross-reference with vessel characteristics

3. **Name Standardization**
   - Handle common naming variations
   - Remove prefixes/suffixes consistently
   - Flag vessels with frequent name changes

### Duplicate Detection Rules
```python
def detect_duplicate_vessels(vessel_data):
    # Strong match: Same IMO
    if imo_matches_existing(vessel_data.imo):
        return "DUPLICATE_IMO"
    
    # Medium match: Same MMSI + similar characteristics
    if mmsi_and_characteristics_match(vessel_data):
        return "LIKELY_DUPLICATE"
    
    # Weak match: Same name + similar size
    if name_and_size_similar(vessel_data):
        return "POSSIBLE_DUPLICATE"
    
    return "UNIQUE"
```

## Testing Strategy

### Unit Tests
- Vessel registry service methods
- AIS data processing functions
- Visit tracking calculations
- Duplicate detection algorithms

### Integration Tests
- End-to-end nomination workflow
- AIS data ingestion and processing
- Database consistency checks
- API endpoint functionality

### Performance Tests
- Large vessel registry queries
- Real-time AIS processing
- Historical data analytics
- Concurrent nomination handling

## Rollback Plan

### Migration Safety
1. **Backup Strategy**
   - Full database backup before migration
   - Incremental backups during implementation
   - Point-in-time recovery capability

2. **Feature Flags**
   - Toggle between old and new vessel handling
   - Gradual rollout to subset of users
   - Quick rollback if issues detected

3. **Data Integrity Checks**
   - Verify assignment linking after migration
   - Check vessel record completeness
   - Validate visit tracking accuracy

## Success Metrics

### Functional Metrics
- ✅ Vessel identity persistence across visits
- ✅ Accurate assignment separation by visit
- ✅ AIS data integration working
- ✅ Historical analytics available

### Performance Metrics
- Response time for vessel lookups < 200ms
- AIS data processing lag < 30 seconds  
- Nomination form pre-population < 1 second
- Historical queries < 2 seconds

### Data Quality Metrics
- Vessel duplicate rate < 1%
- AIS linkage success rate > 90%
- Data completeness > 85%
- User satisfaction with new features > 80%

## Future Enhancements

### Phase 6+ (Future)
1. **Machine Learning Integration**
   - Predictive ETA based on historical patterns
   - Automatic cargo type classification
   - Performance optimization recommendations

2. **External Data Integration**
   - Lloyd's List vessel database
   - Port state control records
   - Commercial vessel databases

3. **Advanced Analytics**
   - Vessel performance benchmarking
   - Route optimization suggestions
   - Carbon footprint tracking
   - Predictive maintenance alerts

4. **Mobile Applications**
   - Vessel tracking mobile app
   - Push notifications for status changes
   - Offline capability for critical data

This comprehensive plan ensures logical implementation while maintaining system stability and providing clear benefits for vessel tracking and management.