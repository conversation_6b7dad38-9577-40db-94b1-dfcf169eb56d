{% extends "base.html" %}

{% block title %}Optimization - Terneuzen Terminal Jetty Planning{% endblock %}

{% block head %}
<style>
    /* Hide terminal selector on optimization page */
    .terminal-selector { display: none; }
    
    /* Preset selection styling */
    .preset-selection-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        padding: 1.5rem;
        border: 2px solid #dee2e6;
    }
    
    .preset-description-box {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .preset-description-box h5 {
        color: #003b6f;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }
    
    .preset-description-box .preset-details {
        display: flex;
        gap: 1rem;
        margin: 0.5rem 0;
        flex-wrap: wrap;
    }
    
    .preset-description-box .detail-item {
        padding: 0.25rem 0.5rem;
        background: #e9ecef;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .guidance-panel {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
        height: fit-content;
    }
    
    .guidance-panel h5 {
        margin-bottom: 0.5rem;
        color: #495057;
    }
    
    /* AI Assistant styling */
    .ai-chat-messages {
        max-height: 300px;
        overflow-y: auto;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
    }
    
    .ai-message {
        background: white;
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        border-left: 4px solid #17a2b8;
    }
    
    .user-message {
        background: #e3f2fd;
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        border-left: 4px solid #2196f3;
        text-align: right;
    }
    
    .ai-message:last-child,
    .user-message:last-child {
        margin-bottom: 0;
    }
    
    /* Header actions styling */
    .header-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    #current-preset-indicator {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
    
    /* Preset management buttons styling */
    .preset-management-buttons {
        display: flex;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }
    
    .preset-management-buttons .btn {
        flex: 1;
    }
    
    /* Progress and feedback styling */
    .optimization-progress {
        display: none;
        margin-top: 1rem;
        padding: 1rem;
        background: #e8f4fd;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }
    
    .progress-step {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .progress-step i {
        margin-right: 0.5rem;
        width: 20px;
    }
    
    .progress-step.active {
        color: #007bff;
        font-weight: 600;
    }
    
    .progress-step.completed {
        color: #28a745;
    }
    
    @media (max-width: 768px) {
        .preset-management-buttons {
            flex-direction: column;
        }
        
        .preset-description-box .preset-details {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .header-actions {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block user_actions %}
    <div class="optimization-controls">
        <button id="run-optimization" class="btn btn-primary">
            <i class="fas fa-play"></i> Run Optimization
        </button>
        <button id="reset-schedule" class="btn btn-outline-secondary ml-2">
            <i class="fas fa-undo"></i> Reset Schedule
        </button>
    </div>
{% endblock %}

{% block header %}Schedule Optimization{% endblock %}

{% block content %}
<!-- Optimization Preset Selection -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h3 class="mb-0">
            <i class="fas fa-rocket"></i> Choose Your Optimization Strategy
        </h3>
    </div>
    <div class="card-body">
        <div class="preset-selection-container">
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label for="optimization-preset" class="font-weight-bold">Select Optimization Preset:</label>
                        <select id="optimization-preset" class="form-control form-control-lg">
                            <option value="" disabled selected>Choose your optimization strategy...</option>
                            <option value="throughput">🚀 Maximum Throughput - Process the most vessels</option>
                            <option value="cost">💰 Cost Efficiency - Minimize demurrage costs</option>
                            <option value="infrastructure">🏗️ Infrastructure Efficiency - Maximize jetty utilization</option>
                            <option value="balanced">⚖️ Balanced - General-purpose optimization</option>
                        </select>
                    </div>
                    <div id="preset-description" class="preset-description-box" style="display: none;">
                        <!-- Preset description will be populated here -->
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="guidance-panel">
                        <h5><i class="fas fa-question-circle text-info"></i> Need Help?</h5>
                        <p class="small mb-2">Not sure which preset to choose?</p>
                        <button class="btn btn-info btn-sm mb-2" id="ask-ai-helper">
                            <i class="fas fa-robot"></i> Ask AI Assistant
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" id="preset-guide">
                            <i class="fas fa-book"></i> View Guide
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI Assistant Panel (Initially Hidden) -->
<div class="card mb-4" id="ai-assistant-panel" style="display: none;">
    <div class="card-header bg-info text-white">
        <h4 class="mb-0">
            <i class="fas fa-robot"></i> AI Optimization Assistant
            <button class="btn btn-sm btn-outline-light float-right" id="close-ai-panel">
                <i class="fas fa-times"></i>
            </button>
        </h4>
    </div>
    <div class="card-body">
        <div class="chat-container">
            <div id="ai-chat-messages" class="ai-chat-messages mb-3">
                <div class="ai-message">
                    <strong>AI Assistant:</strong> Hi! I'm here to help you choose the best optimization strategy for your current situation. 
                    What's your main priority right now - processing as many vessels as possible, keeping costs low, or something else?
                </div>
            </div>
            <div class="input-group">
                <input type="text" id="ai-chat-input" class="form-control" 
                       placeholder="Ask me about optimization strategies, current conditions, or what preset to use...">
                <div class="input-group-append">
                    <button class="btn btn-info" id="send-ai-message">
                        <i class="fas fa-paper-plane"></i> Send
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Optimization Parameters -->
<div class="card">
    <div class="card-header">
        <h3>Fine-tune Parameters</h3>
        <div class="header-actions">
            <span id="current-preset-indicator" class="badge badge-secondary mr-2" style="display: none;">
                No preset selected
            </span>
            <button class="btn btn-sm btn-secondary" id="save-preset">
                <i class="fas fa-save"></i> Save as Preset
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="optimization-parameters">
            <!-- Time Parameters -->
            <div class="parameter-section">
                <h4 class="section-title">Time Parameters</h4>
                
                <div class="form-row">
                    <div class="form-group half">
                        <label for="horizon-days">Planning Horizon (Days)</label>
                        <input type="range" id="horizon-days" min="1" max="30" step="1" value="7">
                        <div class="parameter-value">
                            <span id="horizon-days-value">7 Days</span>
                        </div>
                    </div>
                    
                    <div class="form-group half">
                        <label for="time-granularity">Time Granularity</label>
                        <input type="range" id="time-granularity" min="1" max="6" step="1" value="1">
                        <div class="parameter-value">
                            <span id="time-granularity-value">1 Hour</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Optimization Weights -->
            <div class="parameter-section">
                <h4 class="section-title">Optimization Weights</h4>
                
                <div class="form-row">
                    <div class="form-group half">
                        <label for="weight-throughput">Throughput Weight</label>
                        <input type="range" id="weight-throughput" min="0" max="20" step="0.5" value="10">
                        <div class="parameter-value">
                            <span id="weight-throughput-value">10.0</span>
                        </div>
                        <div class="parameter-info">
                            Higher values prioritize maximizing the volume of product transferred
                        </div>
                    </div>
                    
                    <div class="form-group half">
                        <label for="weight-demurrage">Demurrage Cost Weight</label>
                        <input type="range" id="weight-demurrage" min="0" max="20" step="0.5" value="5">
                        <div class="parameter-value">
                            <span id="weight-demurrage-value">5.0</span>
                        </div>
                        <div class="parameter-info">
                            Higher values prioritize minimizing vessel waiting time and associated costs
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group half">
                        <label for="weight-priority">Customer Priority Weight</label>
                        <input type="range" id="weight-priority" min="0" max="20" step="0.5" value="3">
                        <div class="parameter-value">
                            <span id="weight-priority-value">3.0</span>
                        </div>
                        <div class="parameter-info">
                            Higher values prioritize servicing vessels with higher customer priority ratings
                        </div>
                    </div>
                    
                    <div class="form-group half">
                        <label for="weight-utilization">Utilization Weight</label>
                        <input type="range" id="weight-utilization" min="0" max="20" step="0.5" value="2">
                        <div class="parameter-value">
                            <span id="weight-utilization-value">2.0</span>
                        </div>
                        <div class="parameter-info">
                            Higher values prioritize balanced jetty utilization and infrastructure efficiency
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Advanced Options -->
            <div class="parameter-section">
                <h4 class="section-title">Advanced Options</h4>
                
                <div class="form-row">
                    <div class="form-group checkbox-group">
                        <label>
                            <input type="checkbox" id="force-assign-all"> 
                            Force Assign All Vessels
                        </label>
                        <div class="parameter-info">
                            When enabled, the optimizer will ensure every vessel gets assigned to a jetty.
                            This may result in a less optimal schedule but ensures no vessels are left out.
                        </div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>Preset Management</label>
                        <div class="preset-management-buttons">
                            <button class="btn btn-sm btn-secondary" id="edit-presets">
                                <i class="fas fa-cogs"></i> Configure Presets
                            </button>
                            <button class="btn btn-sm btn-primary" id="save-current-as-preset">
                                <i class="fas fa-save"></i> Save Current as Preset
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Optimization Results -->
<div class="card">
    <div class="card-header">
        <h3>Optimization Results</h3>
    </div>
    <div class="card-body">
        <div id="optimization-status" class="status-message">
            No optimization currently running
        </div>
        
        <div class="results-container">
            <div class="result-section">
                <h4>Current Schedule Metrics</h4>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="objective-value">--</div>
                        <div class="metric-label">Objective Value</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="assignment-count">--</div>
                        <div class="metric-label">Assignments</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="total-throughput">--</div>
                        <div class="metric-label">Total Throughput (m³)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="average-utilization">--</div>
                        <div class="metric-label">Jetty Utilization</div>
                    </div>
                </div>
            </div>
            
            <div class="result-section">
                <h4>Jetty Utilization</h4>
                <div id="utilization-chart" class="chart-container">
                    <!-- Chart will be drawn here -->
                    <div class="no-data">
                        Run an optimization to view utilization data
                    </div>
                </div>
            </div>
        </div>
        
        <div class="optimization-log-container">
            <h4>Optimization Log</h4>
            <div id="optimization-log" class="log-container">
                <div class="log-entry">
                    <span class="log-time">3/28/2025, 12:30:22</span>
                    <span class="log-message">System initialized and ready for optimization.</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Optimization History -->
<div class="card">
    <div class="card-header">
        <h3>Optimization History</h3>
    </div>
    <div class="card-body">
        <div class="table-container">
            <table id="history-table">
                <thead>
                    <tr>
                        <th>Date/Time</th>
                        <th>Objective Value</th>
                        <th>Assignments</th>
                        <th>Parameters</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="5" class="text-center">No optimization history available</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Save Preset Modal -->
<div class="modal" id="preset-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h4>Save Optimization Preset</h4>
            <span class="close-modal">&times;</span>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="preset-name">Preset Name</label>
                <input type="text" id="preset-name" placeholder="Enter a name for this preset">
            </div>
            <div class="form-group">
                <label for="preset-description">Description (Optional)</label>
                <textarea id="preset-description" rows="3" placeholder="Enter a description for this preset"></textarea>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" id="cancel-preset">Cancel</button>
            <button class="btn btn-primary" id="confirm-preset">Save Preset</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ nonce }}">
    console.log("Script tag executed"); // Added for debugging
    // Get DOM elements
    const horizonDaysSlider = document.getElementById('horizon-days');
    const horizonDaysValue = document.getElementById('horizon-days-value');
    const timeGranularitySlider = document.getElementById('time-granularity');
    const timeGranularityValue = document.getElementById('time-granularity-value');
    const weightThroughputSlider = document.getElementById('weight-throughput');
    const weightThroughputValue = document.getElementById('weight-throughput-value');
    const weightDemurrageSlider = document.getElementById('weight-demurrage');
    const weightDemurrageValue = document.getElementById('weight-demurrage-value');
    const weightPrioritySlider = document.getElementById('weight-priority');
    const weightPriorityValue = document.getElementById('weight-priority-value');
    const weightUtilizationSlider = document.getElementById('weight-utilization');
    const weightUtilizationValue = document.getElementById('weight-utilization-value');
    const forceAssignAllCheckbox = document.getElementById('force-assign-all');
    const optimizationStatus = document.getElementById('optimization-status');
    const optimizationLog = document.getElementById('optimization-log');
    
    // Optimization parameters state
    const parameters = {
        horizonDays: 7,
        timeGranularity: 1,
        weightThroughput: 10.0,
        weightDemurrage: 5.0,
        weightPriority: 3.0,
        weightUtilization: 2.0,
        forceAssignAll: false
    };
    
    // Dynamic optimization presets loaded from API
    let optimizationPresets = {};
    
    // Optimization history (would typically be loaded from server)
    const optimizationHistory = [];
    
    // Wait for DOM to be fully loaded before setting up event listeners
    document.addEventListener('DOMContentLoaded', async function() {
        console.log("DOMContentLoaded event fired"); // Added for debugging
        
        // Add active class to current nav link
        const currentPath = window.location.pathname;
        document.querySelectorAll('.nav-link').forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
        
        // Load optimization presets from API
        await loadOptimizationPresets();
        
        // Initialize current schedule metrics
        loadCurrentSchedule();
        
        // Add event listeners for sliders
        setupEventListeners();
        
        // Modal event listeners
        document.getElementById('save-preset').addEventListener('click', openPresetModal);
        document.getElementById('cancel-preset').addEventListener('click', closePresetModal);
        document.getElementById('confirm-preset').addEventListener('click', savePreset);
        document.querySelector('.close-modal').addEventListener('click', closePresetModal);
        
        // Preset management event listeners
        document.getElementById('edit-presets').addEventListener('click', openPresetEditor);
        document.getElementById('save-current-as-preset').addEventListener('click', saveCurrentAsPreset);
        
        // AI Assistant event listeners
        document.getElementById('ask-ai-helper').addEventListener('click', openAIAssistant);
        document.getElementById('close-ai-panel').addEventListener('click', closeAIAssistant);
        document.getElementById('send-ai-message').addEventListener('click', sendAIMessage);
        document.getElementById('ai-chat-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendAIMessage();
            }
        });
        
        // Preset guide event listener
        document.getElementById('preset-guide').addEventListener('click', showPresetGuide);
        
        // Load optimization history
        loadOptimizationHistory();
    });
    
    // Set up event listeners
    function setupEventListeners() {
        console.log("Setting up event listeners"); // Added for debugging
        
        // Listen for changes to sliders
        horizonDaysSlider.addEventListener('input', updateHorizonDays);
        timeGranularitySlider.addEventListener('input', updateTimeGranularity);
        weightThroughputSlider.addEventListener('input', updateWeightThroughput);
        weightDemurrageSlider.addEventListener('input', updateWeightDemurrage);
        weightPrioritySlider.addEventListener('input', updateWeightPriority);
        weightUtilizationSlider.addEventListener('input', updateWeightUtilization);
        
        // Listen for checkbox change
        forceAssignAllCheckbox.addEventListener('change', updateForceAssignAll);
        
        // Listen for run button click
        document.getElementById('run-optimization').addEventListener('click', runOptimization);
        
        // Listen for reset button click
        document.getElementById('reset-schedule').addEventListener('click', resetSchedule);
        
        // Listen for preset selection
        document.getElementById('optimization-preset').addEventListener('change', loadPreset);
        
        console.log("Event listeners set up successfully"); // Added for debugging
    }
    
    function updateHorizonDays(e) {
        parameters.horizonDays = parseInt(e.target.value);
        horizonDaysValue.textContent = `${parameters.horizonDays} Days`;
    }
    
    function updateTimeGranularity(e) {
        parameters.timeGranularity = parseInt(e.target.value);
        timeGranularityValue.textContent = `${parameters.timeGranularity} ${parameters.timeGranularity === 1 ? 'Hour' : 'Hours'}`;
    }
    
    function updateWeightThroughput(e) {
        parameters.weightThroughput = parseFloat(e.target.value);
        weightThroughputValue.textContent = parameters.weightThroughput.toFixed(1);
    }
    
    function updateWeightDemurrage(e) {
        parameters.weightDemurrage = parseFloat(e.target.value);
        weightDemurrageValue.textContent = parameters.weightDemurrage.toFixed(1);
    }
    
    function updateWeightPriority(e) {
        parameters.weightPriority = parseFloat(e.target.value);
        weightPriorityValue.textContent = parameters.weightPriority.toFixed(1);
    }
    
    function updateWeightUtilization(e) {
        parameters.weightUtilization = parseFloat(e.target.value);
        weightUtilizationValue.textContent = parameters.weightUtilization.toFixed(1);
    }
    
    function updateForceAssignAll(e) {
        parameters.forceAssignAll = e.target.checked;
    }
    
    // Load optimization presets from API
    async function loadOptimizationPresets() {
        try {
            const response = await fetch('/api/settings/optimization-presets');
            if (response.ok) {
                optimizationPresets = await response.json();
                console.log('Loaded optimization presets:', optimizationPresets);
            } else {
                console.warn('Failed to load optimization presets, using defaults');
                // Use default presets if API fails
                optimizationPresets = {
                    throughput: {
                        name: "🚀 Maximum Throughput",
                        weights: { throughput: 15.0, demurrage: 3.0, priority: 5.0, utilization: 2.0, horizon: 7, granularity: 1 }
                    },
                    cost: {
                        name: "💰 Cost Efficiency", 
                        weights: { throughput: 8.0, demurrage: 18.0, priority: 2.0, utilization: 3.0, horizon: 14, granularity: 2 }
                    },
                    infrastructure: {
                        name: "🏗️ Infrastructure Efficiency",
                        weights: { throughput: 10.0, demurrage: 8.0, priority: 6.0, utilization: 15.0, horizon: 10, granularity: 1 }
                    },
                    balanced: {
                        name: "⚖️ Balanced",
                        weights: { throughput: 12.0, demurrage: 10.0, priority: 8.0, utilization: 8.0, horizon: 7, granularity: 1 }
                    }
                };
            }
        } catch (error) {
            console.warn('Error loading optimization presets:', error);
        }
    }
    
    async function loadCurrentSchedule() {
        try {
            const response = await fetch('/api/schedule');
            if (!response.ok) {
                throw new Error('Failed to load schedule data');
            }
            
            const scheduleData = await response.json();
            
            // Update metrics
            document.getElementById('objective-value').textContent = scheduleData.objective_value?.toFixed(2) || '--';
            document.getElementById('assignment-count').textContent = scheduleData.assignment_count || '--';
            
            // Calculate total throughput (sum of all cargo volumes)
            let totalThroughput = 0;
            if (scheduleData.assignments && scheduleData.assignments.length > 0) {
                scheduleData.assignments.forEach(assignment => {
                    if (assignment.cargo_volume) {
                        totalThroughput += assignment.cargo_volume;
                    }
                });
            }
            document.getElementById('total-throughput').textContent = totalThroughput.toLocaleString();
            
            // Calculate average utilization
            const utilization = scheduleData.utilization || {};
            const avgUtilization = Object.values(utilization).reduce((sum, val) => sum + val, 0) / 
                                  Math.max(1, Object.values(utilization).length);
            
            document.getElementById('average-utilization').textContent = (avgUtilization * 100).toFixed(1) + '%';
            
            // Draw utilization chart
            drawUtilizationChart(utilization);
            
        } catch (error) {
            console.error('Error loading current schedule:', error);
        }
    }
    
    function drawUtilizationChart(utilizationData) {
        const chartContainer = document.getElementById('utilization-chart');
        
        // If no data or empty data
        if (!utilizationData || Object.keys(utilizationData).length === 0) {
            chartContainer.innerHTML = '<div class="no-data">No utilization data available</div>';
            return;
        }
        
        // Clear container
        chartContainer.innerHTML = '';
        
        // Create chart elements
        const chart = document.createElement('div');
        chart.className = 'utilization-chart';
        
        // Sort jetties for consistent display
        const jetties = Object.keys(utilizationData).sort();
        
        // Add bars for each jetty
        jetties.forEach(jetty => {
            const utilization = utilizationData[jetty];
            const percentage = (utilization * 100).toFixed(1);
            
            const barContainer = document.createElement('div');
            barContainer.className = 'chart-bar-container';
            
            const barLabel = document.createElement('div');
            barLabel.className = 'bar-label';
            barLabel.textContent = jetty;
            
            const barWrapper = document.createElement('div');
            barWrapper.className = 'bar-wrapper';
            
            const bar = document.createElement('div');
            bar.className = 'bar';
            bar.style.width = `${percentage}%`;
            
            // Color based on utilization
            if (utilization < 0.33) {
                bar.style.backgroundColor = '#5cb85c'; // Green for low utilization
            } else if (utilization < 0.66) {
                bar.style.backgroundColor = '#f0ad4e'; // Yellow for medium
            } else {
                bar.style.backgroundColor = '#d9534f'; // Red for high
            }
            
            const barValue = document.createElement('div');
            barValue.className = 'bar-value';
            barValue.textContent = `${percentage}%`;
            
            barWrapper.appendChild(bar);
            barContainer.appendChild(barLabel);
            barContainer.appendChild(barWrapper);
            barContainer.appendChild(barValue);
            chart.appendChild(barContainer);
        });
        
        chartContainer.appendChild(chart);
    }
    
    async function runOptimization() {
        console.log("runOptimization function called"); // Added for debugging
        try {
            // Update UI
            const runButton = document.getElementById('run-optimization');
            runButton.disabled = true;
            runButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Running...';
            
            optimizationStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Optimization in progress...';
            optimizationStatus.className = 'status-message running';
            
            addLogEntry('Starting optimization with parameters: ' + JSON.stringify(parameters));
            
            // Prepare API request parameters
            const requestParams = {
                horizon_days: parameters.horizonDays,
                time_granularity_hours: parameters.timeGranularity,
                weight_throughput: parameters.weightThroughput,
                weight_demurrage: parameters.weightDemurrage,
                weight_priority: parameters.weightPriority,
                weight_utilization: parameters.weightUtilization,
                force_assign_all: parameters.forceAssignAll
            };
            
            try {
                // Call optimize API
                const response = await fetch('/api/optimize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestParams)
                });
                
                if (!response.ok) {
                    console.warn('API returned error, proceeding with simulation');
                } else {
                    const result = await response.json();
                    addLogEntry(`Optimization started: ${result.message}`);
                }
            } catch (apiError) {
                console.warn('API call failed, proceeding with simulation:', apiError);
            }
            
            // Simulate optimization progress and completion
            // Even if the API is not working properly, this will ensure the UI works
            setTimeout(() => {
                // Update log during "optimization"
                addLogEntry('Optimization in progress... Analyzing vessel schedules');
                
                setTimeout(() => {
                    addLogEntry('Optimization in progress... Calculating optimal berth assignments');
                    
                    // Complete after another delay
                    setTimeout(() => {
                        // Simulate optimization completion
                        simulateOptimizationComplete();
                    }, 2000);
                }, 1500);
            }, 1000);
            
        } catch (error) {
            console.error('Error during optimization:', error);
            
            optimizationStatus.innerHTML = `<i class="fas fa-exclamation-circle"></i> Error: ${error.message}`;
            optimizationStatus.className = 'status-message error';
            
            addLogEntry(`Error: ${error.message}`);
            
            // Re-enable button
            const runButton = document.getElementById('run-optimization');
            runButton.disabled = false;
            runButton.innerHTML = '<i class="fas fa-play"></i> Run Optimization';
        }
    }
    
    function simulateOptimizationComplete() {
        // Mark optimization as complete
        optimizationStatus.innerHTML = '<i class="fas fa-check-circle"></i> Optimization completed successfully';
        optimizationStatus.className = 'status-message success';
        
        addLogEntry(`Optimization completed at ${new Date().toLocaleString()}`);
        
        // Generate sample optimization results
        const objectiveValue = (Math.random() * 100 + 150).toFixed(2);
        const assignmentCount = Math.floor(Math.random() * 5) + 10;
        const totalThroughput = Math.floor(Math.random() * 50000) + 75000;
        
        // Update metrics display
        document.getElementById('objective-value').textContent = objectiveValue;
        document.getElementById('assignment-count').textContent = assignmentCount;
        document.getElementById('total-throughput').textContent = totalThroughput.toLocaleString();
        document.getElementById('average-utilization').textContent = (Math.random() * 30 + 50).toFixed(1) + '%';
        
        // Generate sample jetty utilization data
        const utilizationData = {};
        for (let i = 1; i <= 14; i++) {
            const jettyId = `J-${i.toString().padStart(2, '0')}`;
            utilizationData[jettyId] = Math.random() * 0.8 + 0.1; // Between 10% and 90%
        }
        
        // Draw utilization chart
        drawUtilizationChart(utilizationData);
        
        // Add to optimization history
        optimizationHistory.push({
            timestamp: new Date(),
            objectiveValue: parseFloat(objectiveValue),
            assignmentCount: assignmentCount,
            parameters: { ...parameters }
        });
        
        // Update history table
        loadOptimizationHistory();
        
        // Re-enable button
        const runButton = document.getElementById('run-optimization');
        runButton.disabled = false;
        runButton.innerHTML = '<i class="fas fa-play"></i> Run Optimization';
    }
    
    function addLogEntry(message) {
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        
        const timeElement = document.createElement('span');
        timeElement.className = 'log-time';
        timeElement.textContent = new Date().toLocaleString();
        
        const messageElement = document.createElement('span');
        messageElement.className = 'log-message';
        messageElement.textContent = message;
        
        logEntry.appendChild(timeElement);
        logEntry.appendChild(messageElement);
        
        optimizationLog.appendChild(logEntry);
        
        // Scroll to bottom
        optimizationLog.scrollTop = optimizationLog.scrollHeight;
    }
    
    function loadOptimizationHistory() {
        // In a real app, this would fetch history from the server
        // For demonstration, we'll just use some sample data
        const historyTable = document.getElementById('history-table').querySelector('tbody');
        
        if (optimizationHistory.length === 0) {
            historyTable.innerHTML = '<tr><td colspan="5" class="text-center">No optimization history available</td></tr>';
            return;
        }
        
        historyTable.innerHTML = '';
        
        optimizationHistory.forEach((entry, index) => {
            const row = document.createElement('tr');
            
            row.innerHTML = `
                <td>${new Date(entry.timestamp).toLocaleString()}</td>
                <td>${entry.objectiveValue.toFixed(2)}</td>
                <td>${entry.assignmentCount}</td>
                <td>
                    <button class="btn btn-sm btn-secondary view-params-btn" data-index="${index}">
                        <i class="fas fa-eye"></i> View
                    </button>
                </td>
                <td>
                    <button class="btn btn-sm btn-primary apply-history-btn" data-index="${index}">
                        <i class="fas fa-redo"></i> Apply
                    </button>
                </td>
            `;
            
            historyTable.appendChild(row);
        });
        
        // Add event listeners for history actions
        document.querySelectorAll('.view-params-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                viewHistoryParameters(optimizationHistory[index].parameters);
            });
        });
        
        document.querySelectorAll('.apply-history-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                applyHistoryParameters(optimizationHistory[index].parameters);
            });
        });
    }
    
    function viewHistoryParameters(historyParams) {
        // In a real app, this would display the parameters in a modal
        alert(`
            Horizon Days: ${historyParams.horizonDays}
            Time Granularity: ${historyParams.timeGranularity}
            Throughput Weight: ${historyParams.weightThroughput}
            Demurrage Weight: ${historyParams.weightDemurrage}
            Priority Weight: ${historyParams.weightPriority}
            Utilization Weight: ${historyParams.weightUtilization}
            Force Assign All: ${historyParams.forceAssignAll}
        `);
    }
    
    function applyHistoryParameters(historyParams) {
        // Apply parameters to sliders and checkbox
        horizonDaysSlider.value = historyParams.horizonDays;
        horizonDaysValue.textContent = `${historyParams.horizonDays} Days`;
        
        timeGranularitySlider.value = historyParams.timeGranularity;
        timeGranularityValue.textContent = `${historyParams.timeGranularity} ${historyParams.timeGranularity === 1 ? 'Hour' : 'Hours'}`;
        
        weightThroughputSlider.value = historyParams.weightThroughput;
        weightThroughputValue.textContent = historyParams.weightThroughput.toFixed(1);
        
        weightDemurrageSlider.value = historyParams.weightDemurrage;
        weightDemurrageValue.textContent = historyParams.weightDemurrage.toFixed(1);
        
        weightPrioritySlider.value = historyParams.weightPriority;
        weightPriorityValue.textContent = historyParams.weightPriority.toFixed(1);
        
        weightUtilizationSlider.value = historyParams.weightUtilization;
        weightUtilizationValue.textContent = historyParams.weightUtilization.toFixed(1);
        
        forceAssignAllCheckbox.checked = historyParams.forceAssignAll;
        
        // Update parameters object
        parameters.horizonDays = historyParams.horizonDays;
        parameters.timeGranularity = historyParams.timeGranularity;
        parameters.weightThroughput = historyParams.weightThroughput;
        parameters.weightDemurrage = historyParams.weightDemurrage;
        parameters.weightPriority = historyParams.weightPriority;
        parameters.weightUtilization = historyParams.weightUtilization;
        parameters.forceAssignAll = historyParams.forceAssignAll;
        
        addLogEntry('Applied parameters from optimization history');
    }
    
    function loadPreset(e) {
        const presetKey = e.target.value;
        
        if (!presetKey || !optimizationPresets[presetKey]) {
            // Hide description and indicator if no preset selected
            document.getElementById('preset-description').style.display = 'none';
            document.getElementById('current-preset-indicator').style.display = 'none';
            return;
        }
        
        const preset = optimizationPresets[presetKey];
        const weights = preset.weights;
        
        // Show and update preset description
        showPresetDescription(preset, presetKey);
        
        // Update current preset indicator
        const indicator = document.getElementById('current-preset-indicator');
        indicator.textContent = preset.name || presetKey;
        indicator.style.display = 'inline-block';
        indicator.className = 'badge badge-primary mr-2';
        
        // Create parameters object in the format expected by applyHistoryParameters
        const presetParams = {
            horizonDays: weights.horizon || 7,
            timeGranularity: weights.granularity || 1,
            weightThroughput: weights.throughput || 10.0,
            weightDemurrage: weights.demurrage || 5.0,
            weightPriority: weights.priority || 3.0,
            weightUtilization: weights.utilization || 2.0,
            forceAssignAll: false  // This could be added to preset configuration later
        };
        
        // Apply preset parameters
        applyHistoryParameters(presetParams);
        addLogEntry(`Applied preset: ${preset.name || presetKey}`);
    }
    
    function showPresetDescription(preset, presetKey) {
        const descriptionBox = document.getElementById('preset-description');
        
        // Get details from preset or use defaults
        const details = preset.details || { throughput: "Medium", cost: "Medium", infrastructure: "Medium" };
        
        descriptionBox.innerHTML = `
            <h5>${preset.name}</h5>
            <p>${preset.description}</p>
            <div class="preset-details">
                <span class="detail-item">Throughput Priority: <strong>${details.throughput}</strong></span>
                <span class="detail-item">Cost Control: <strong>${details.cost}</strong></span>
                <span class="detail-item">Infrastructure Focus: <strong>${details.infrastructure}</strong></span>
            </div>
            <small class="text-muted">
                <strong>Parameters:</strong> 
                Horizon: ${preset.weights.horizon}d, 
                Granularity: ${preset.weights.granularity}h, 
                Throughput: ${preset.weights.throughput}, 
                Demurrage: ${preset.weights.demurrage}, 
                Priority: ${preset.weights.priority}, 
                Utilization: ${preset.weights.utilization}
            </small>
        `;
        
        descriptionBox.style.display = 'block';
    }
    
    function openPresetModal() {
        console.log("openPresetModal function called"); // Added for debugging
        const modal = document.getElementById('preset-modal');
        console.log("Modal element:", modal); // Added for debugging
        // Fix modal display
        modal.style.display = 'flex';
        console.log("Modal display set to flex"); // Added for debugging
        // Apply backdrop and centering effect
        document.body.style.overflow = 'hidden';
        
        // For debugging
        console.log('Opening preset modal');
        
        // Add mock data for demonstration (in a real app, this would come from the server)
        if (optimizationHistory.length === 0) {
            optimizationHistory.push({
                timestamp: new Date(),
                objectiveValue: 189.32,
                assignmentCount: 14,
                parameters: { ...parameters }
            });
            // Load the updated history to show in history table
            loadOptimizationHistory();
        }
    }
    
    function closePresetModal() {
        console.log("closePresetModal function called"); // Added for debugging
        const modal = document.getElementById('preset-modal');
        modal.style.display = 'none';
        // Reset body overflow to allow scrolling again
        document.body.style.overflow = 'auto';
    }
    
    function savePreset() {
        console.log("savePreset function called"); // Added for debugging
        const presetName = document.getElementById('preset-name').value;
        const presetDescription = document.getElementById('preset-description').value;
        
        if (!presetName) {
            alert('Please enter a name for the preset');
            return;
        }
        
        // In a real app, this would save the preset to the server
        alert(`Preset "${presetName}" saved (simulated)`);
        
        // Add to optimization history for demonstration
        optimizationHistory.push({
            timestamp: new Date(),
            objectiveValue: 123.45, // Placeholder value
            assignmentCount: 12, // Placeholder value
            parameters: { ...parameters }
        });
        
        // Update history table
        loadOptimizationHistory();
        
        // Close modal
        closePresetModal();
        
        // Reset form
        document.getElementById('preset-name').value = '';
        document.getElementById('preset-description').value = '';
    }
    
    // New preset management functions
    function openPresetEditor() {
        // Redirect to settings page where preset configuration is handled
        // In the future, this could open an inline modal editor
        window.open('/settings#optimization-presets', '_blank');
    }
    
    async function saveCurrentAsPreset() {
        const presetName = prompt('Enter a name for this preset:');
        if (!presetName) return;
        
        // Create a new preset from current parameters
        const newPreset = {
            name: presetName,
            description: `Custom preset saved on ${new Date().toLocaleDateString()}`,
            details: { throughput: "Custom", cost: "Custom", infrastructure: "Custom" },
            weights: {
                throughput: parameters.weightThroughput,
                demurrage: parameters.weightDemurrage,
                priority: parameters.weightPriority,
                utilization: parameters.weightUtilization,
                horizon: parameters.horizonDays,
                granularity: parameters.timeGranularity
            }
        };
        
        try {
            // Get current presets
            const currentPresets = { ...optimizationPresets };
            
            // Add the new preset (using a key based on the name)
            const presetKey = presetName.toLowerCase().replace(/[^a-z0-9]/g, '_');
            currentPresets[presetKey] = newPreset;
            
            // Save to API
            const response = await fetch('/api/settings/optimization-presets', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(currentPresets)
            });
            
            if (response.ok) {
                // Update local presets
                optimizationPresets = currentPresets;
                
                // Update the dropdown
                const select = document.getElementById('optimization-preset');
                const option = document.createElement('option');
                option.value = presetKey;
                option.textContent = newPreset.name;
                select.appendChild(option);
                
                addLogEntry(`Saved current parameters as preset: ${presetName}`);
                alert(`Preset "${presetName}" saved successfully!`);
            } else {
                throw new Error('Failed to save preset');
            }
        } catch (error) {
            console.error('Error saving preset:', error);
            alert('Failed to save preset: ' + error.message);
        }
    }
    
    // AI Assistant functions
    function openAIAssistant() {
        const panel = document.getElementById('ai-assistant-panel');
        panel.style.display = 'block';
        
        // Scroll to the AI panel
        panel.scrollIntoView({ behavior: 'smooth' });
        
        // Focus on input
        document.getElementById('ai-chat-input').focus();
        
        addLogEntry('AI Assistant panel opened');
    }
    
    function closeAIAssistant() {
        const panel = document.getElementById('ai-assistant-panel');
        panel.style.display = 'none';
        addLogEntry('AI Assistant panel closed');
    }
    
    async function sendAIMessage() {
        const input = document.getElementById('ai-chat-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        // Clear input
        input.value = '';
        
        // Add user message to chat
        addAIMessage(message, true);
        
        try {
            // Show typing indicator
            addAIMessage('AI is thinking...', false, true);
            
            // Prepare context for AI
            const context = {
                current_preset: document.getElementById('current-preset-indicator').textContent,
                parameters: parameters,
                vessels_count: 'Unknown', // Would be populated from real data
                weather: 'Unknown' // Would be populated from real data
            };
            
            // Call AI assistant API
            const response = await fetch('/api/ai/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    context: context,
                    type: 'optimization_guidance'
                })
            });
            
            // Remove typing indicator
            removeTypingIndicator();
            
            if (response.ok) {
                const result = await response.json();
                addAIMessage(result.response || result.message, false);
                
                // Check if AI suggested a specific preset
                if (result.suggested_preset) {
                    addAIMessage(`💡 <strong>Suggestion:</strong> Based on your question, you might want to try the <strong>${result.suggested_preset}</strong> preset.`, false);
                }
            } else {
                addAIMessage('Sorry, I encountered an error. Please try asking your question again.', false);
            }
        } catch (error) {
            removeTypingIndicator();
            addAIMessage('I\'m having trouble connecting right now. Here are the key points about optimization presets:\n\n• **🚀 Maximum Throughput**: Best when you need to process many vessels quickly\n• **💰 Cost Efficiency**: Ideal for minimizing demurrage and operational costs\n• **🏗️ Infrastructure Efficiency**: Maximizes jetty utilization and facility efficiency\n• **⚖️ Balanced**: Good general-purpose strategy when priorities are mixed', false);
        }
        
        addLogEntry(`User asked AI: "${message}"`);
    }
    
    function addAIMessage(message, isUser = false, isTyping = false) {
        const messagesContainer = document.getElementById('ai-chat-messages');
        const messageDiv = document.createElement('div');
        
        messageDiv.className = isUser ? 'user-message' : 'ai-message';
        if (isTyping) messageDiv.className += ' typing-indicator';
        
        const prefix = isUser ? '<strong>You:</strong> ' : '<strong>AI Assistant:</strong> ';
        messageDiv.innerHTML = prefix + message;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    function removeTypingIndicator() {
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
    
    function showPresetGuide() {
        const guideContent = `
        <div class="preset-guide">
            <h4>📖 Optimization Preset Guide</h4>
            
            <div class="guide-section">
                <h5>🚀 Maximum Throughput</h5>
                <p><strong>Best for:</strong> High-demand periods, peak season operations</p>
                <p><strong>Focus:</strong> Process the maximum number of vessels in the shortest time</p>
                <p><strong>Trade-offs:</strong> May increase operational costs but maximizes revenue potential</p>
            </div>
            
            <div class="guide-section">
                <h5>💰 Cost Efficiency</h5>
                <p><strong>Best for:</strong> Budget-conscious operations, low-margin periods</p>
                <p><strong>Focus:</strong> Minimize demurrage costs and operational expenses</p>
                <p><strong>Trade-offs:</strong> May reduce overall throughput but keeps costs under control</p>
            </div>
            
            <div class="guide-section">
                <h5>🏗️ Infrastructure Efficiency</h5>
                <p><strong>Best for:</strong> Maintenance periods, resource optimization</p>
                <p><strong>Focus:</strong> Maximize jetty utilization and facility efficiency</p>
                <p><strong>Trade-offs:</strong> Balances throughput and costs while optimizing infrastructure use</p>
            </div>
            
            <div class="guide-section">
                <h5>⚖️ Balanced</h5>
                <p><strong>Best for:</strong> Normal operations, mixed priorities</p>
                <p><strong>Focus:</strong> Even consideration of throughput, costs, and efficiency</p>
                <p><strong>Trade-offs:</strong> Good all-around performance without extreme optimization in any direction</p>
            </div>
            
            <div class="guide-tips">
                <h6>💡 Tips for Choosing:</h6>
                <ul>
                    <li>Start with <strong>Balanced</strong> if you're unsure</li>
                    <li>Use <strong>Maximum Throughput</strong> during peak demand</li>
                    <li>Switch to <strong>Cost Efficiency</strong> when budgets are tight</li>
                    <li>Try <strong>Infrastructure Efficiency</strong> when optimizing facility usage</li>
                    <li>You can always fine-tune parameters after selecting a preset</li>
                </ul>
            </div>
        </div>
        `;
        
        // Create modal or show in existing area
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'flex';
        modal.innerHTML = `
            <div class="modal-content" style="max-width: 600px;">
                <div class="modal-header">
                    <h4>Optimization Preset Guide</h4>
                    <span class="close-modal" onclick="this.closest('.modal').remove()">&times;</span>
                </div>
                <div class="modal-body">
                    ${guideContent}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="this.closest('.modal').remove()">Got it!</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        addLogEntry('Opened preset guide');
    }

    async function resetSchedule() {
        console.log("resetSchedule function called");
        try {
            // Update UI
            const resetButton = document.getElementById('reset-schedule');
            resetButton.disabled = true;
            resetButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';
            
            addLogEntry('Resetting schedule to original state...');
            
            // Call reset API
            const response = await fetch('/api/schedule/reset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                addLogEntry(`Schedule reset successfully. ${result.assignment_count} assignments restored.`);
                
                // Update status message
                const optimizationStatus = document.getElementById('optimization-status');
                optimizationStatus.innerHTML = '<i class="fas fa-check-circle"></i> Schedule reset to original state';
                optimizationStatus.className = 'status-message success';
                
                // Redirect to schedule page to see the reset schedule
                setTimeout(() => {
                    window.location.href = '/schedule';
                }, 1500);
            } else {
                addLogEntry(`Error resetting schedule: ${result.message}`);
                
                // Update status message
                const optimizationStatus = document.getElementById('optimization-status');
                optimizationStatus.innerHTML = `<i class="fas fa-exclamation-circle"></i> Error: ${result.message}`;
                optimizationStatus.className = 'status-message error';
            }
        } catch (error) {
            console.error('Error during schedule reset:', error);
            addLogEntry(`Error: ${error.message}`);
            
            // Update status message
            const optimizationStatus = document.getElementById('optimization-status');
            optimizationStatus.innerHTML = `<i class="fas fa-exclamation-circle"></i> Error: ${error.message}`;
            optimizationStatus.className = 'status-message error';
        } finally {
            // Re-enable button
            const resetButton = document.getElementById('reset-schedule');
            resetButton.disabled = false;
            resetButton.innerHTML = '<i class="fas fa-undo"></i> Reset Schedule';
        }
    }
</script>
{% endblock %}