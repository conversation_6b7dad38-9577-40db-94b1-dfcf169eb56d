/* EVOS Corporate Style System */
/* Using external Google Fonts for improved loading performance */

:root {
  /* EVOS Color Palette */
  --primary-teal: #00a9a0;
  --secondary-teal: #008c84;
  --dark-teal: #00736c;
  --accent-orange: #ff6d00;
  --accent-orange-light: #ff9e40;
  --accent-orange-dark: #d65600;
  --text-dark: #2d2d2d;
  --text-light: #ffffff;
  --text-medium: #6c757d;
  --background-light: #f8f9fa;
  --background-medium: #e9ecef;
  --success-green: #4caf50;
  --warning-amber: #ff9800;
  --error-red: #e53935;

  /* Layout Variables */
  --header-height: 60px;
  --sidebar-width: 260px;
  --border-radius: 0.375rem;
  --box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  --transition-speed: 0.3s;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Typography */
body {
  font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  background-color: var(--background-light);
  color: var(--text-dark);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Allow synthetic italic/bold since we don't embed all faces */
  font-synthesis: weight style;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Blinker', 'Roboto', sans-serif !important;
  font-weight: 600;
  color: var(--text-dark);
  margin-top: 0;
  margin-bottom: 0.5em;
}

/* Set base font on root, let components (e.g., Font Awesome) override as needed */
html { font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }

h1 { font-size: 2rem; letter-spacing: -0.5px; }
h2 { font-size: 1.75rem; letter-spacing: -0.3px; }
h3 { font-size: 1.5rem; letter-spacing: -0.2px; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.1rem; }
h6 { font-size: 1rem; font-weight: 700; }

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

a {
  color: var(--primary-teal);
  text-decoration: none;
  transition: color var(--transition-speed) ease;
}

a:hover {
  color: var(--secondary-teal);
}

/* Layout */
.container {
  display: grid;
  grid-template-areas:
    "sidebar header"
    "sidebar main";
  grid-template-columns: var(--sidebar-width) 1fr;
  grid-template-rows: var(--header-height) 1fr;
  height: 100vh;
}

/* Header */
.header {
  grid-area: header;
  background-color: var(--primary-teal);
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--box-shadow);
  z-index: 10;
}

.header h1 {
  color: var(--text-light);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.terminal-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.terminal-selector label {
  color: var(--text-light);
  font-weight: 500;
  font-size: 0.9rem;
  white-space: nowrap;
}

.terminal-select {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-light);
  border-radius: var(--border-radius);
  padding: 0.375rem 0.75rem;
  font-size: 0.9rem;
  min-width: 200px;
  transition: all var(--transition-speed) ease;
}

.terminal-select:focus {
  outline: none;
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
}

.terminal-select option {
  background-color: var(--background-light);
  color: var(--text-dark);
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Sidebar */
.sidebar {
  grid-area: sidebar;
  background-color: var(--secondary-teal);
  color: var(--text-light);
  padding: 0;
  overflow-y: auto;
  z-index: 20;
  box-shadow: var(--box-shadow);
}

.brand {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem 1rem;
  background-color: var(--dark-teal);
}

.brand h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-light);
  margin: 0;
}

.evos-logo {
  display: flex;
  align-items: center;
  position: relative;
}

.logo-image {
  height: 24px;
  margin-right: 10px;
}

.nav-list {
  list-style: none;
  padding: 0.5rem 0;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.875rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all var(--transition-speed) ease;
}

.nav-link:hover,
.nav-link.active {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
}

.nav-link i {
  margin-right: 0.75rem;
  font-size: 1.25rem;
  width: 1.5rem;
  text-align: center;
}

/* Main Content */
.main {
  grid-area: main;
  padding: 2rem;
  overflow-y: auto;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1.25rem;
  font-size: 0.95rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all var(--transition-speed) ease;
  border: none;
  cursor: pointer;
}

.btn i {
  margin-right: 0.5rem;
}

.btn-primary {
  background-color: var(--accent-orange);
  color: var(--text-light);
}

.btn-primary:hover {
  background-color: var(--accent-orange-dark);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: var(--secondary-teal);
  color: var(--text-light);
}

.btn-secondary:hover {
  background-color: var(--dark-teal);
  transform: translateY(-1px);
}

.btn-success {
  background-color: var(--success-green);
  color: var(--text-light);
}

.btn-success:hover {
  background-color: #3c9f40;
}

.btn-info {
  background-color: var(--text-light);
  color: var(--primary-teal);
  border: 1px solid var(--text-light);
}

.btn-info:hover {
  background-color: var(--background-light);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.btn-icon {
  width: 2.25rem;
  height: 2.25rem;
  padding: 0;
  border-radius: 50%;
}

/* Cards */
.card {
  background-color: var(--text-light);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: box-shadow var(--transition-speed) ease;
}

.card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 1rem 1.5rem;
  background-color: var(--text-light);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h3 {
  font-size: 1.25rem;
  margin: 0;
  color: var(--primary-teal);
}

.card-body {
  padding: 1.5rem;
}

/* Widgets */
.widget-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.widget {
  background-color: var(--text-light);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: 1.5rem;
  transition: transform var(--transition-speed) ease,
              box-shadow var(--transition-speed) ease;
}

.widget:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.widget-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.widget-title {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-medium);
  margin: 0;
}

.widget-header i {
  font-size: 1.25rem;
  color: var(--primary-teal);
}

.widget-value {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-dark);
  font-family: 'Blinker', sans-serif;
}

.widget-footer {
  margin-top: 1rem;
  display: flex;
  align-items: center;
  color: var(--text-medium);
  font-size: 0.9rem;
}

.widget-footer i {
  margin-right: 0.5rem;
}

/* Tables */
.table-container {
  overflow-x: auto;
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
}

table {
  width: 100%;
  border-collapse: collapse;
}

thead th {
  background-color: var(--primary-teal);
  color: var(--text-light);
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 500;
  white-space: nowrap;
}

td {
  padding: 0.75rem 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  vertical-align: middle;
}

tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Status Indicators */
.status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-indicator.active {
  background-color: var(--success-green);
}

.status-indicator.warning {
  background-color: var(--warning-amber);
}

.status-indicator.error {
  background-color: var(--error-red);
}

.vessel-status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-light);
  text-transform: capitalize;
}

.vessel-status.berthed {
  background-color: var(--success-green);
}

.vessel-status.waiting {
  background-color: var(--warning-amber);
  color: var(--text-dark);
}

.vessel-status.approaching {
  background-color: var(--secondary-teal);
}

.vessel-status.scheduled {
  background-color: var(--primary-teal);
}

/* Schedule View */
.schedule-grid {
  display: grid;
  grid-template-columns: 200px repeat(7, 1fr);
  gap: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius);
  overflow: hidden;
  margin-top: 1rem;
}

.schedule-header {
  background-color: var(--primary-teal);
  color: var(--text-light);
  padding: 0.75rem;
  font-weight: 500;
  text-align: center;
  font-family: 'Blinker', sans-serif;
}

.schedule-row {
  display: contents;
}

.schedule-jetty {
  background-color: var(--secondary-teal);
  color: var(--text-light);
  padding: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.schedule-cell {
  background-color: var(--text-light);
  padding: 0.75rem;
  text-align: center;
  position: relative;
  min-height: 80px;
  transition: background-color var(--transition-speed) ease;
}

.schedule-cell:hover {
  background-color: var(--background-medium);
}

.assignment {
  position: absolute;
  top: 4px;
  bottom: 4px;
  left: 4px;
  right: 4px;
  border-radius: calc(var(--border-radius) / 2);
  padding: 0.5rem;
  font-size: 0.85rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: var(--text-light);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.assignment:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.assignment.vessel {
  background-color: var(--primary-teal);
}

.assignment.barge {
  background-color: var(--accent-orange);
}

.assignment.planned {
  opacity: 0.9;
  border: 2px dashed rgba(255, 255, 255, 0.5);
}

.assignment-vessel {
  font-weight: 600;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.assignment-details {
  font-size: 0.8rem;
  opacity: 0.9;
}

/* Vessel List */
.vessel-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.vessel-card {
  border-radius: var(--border-radius);
  padding: 1.25rem;
  background-color: var(--text-light);
  position: relative;
  box-shadow: var(--card-shadow);
  transition: transform var(--transition-speed) ease,
              box-shadow var(--transition-speed) ease;
}

.vessel-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.vessel-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  font-family: 'Blinker', sans-serif;
  color: var(--primary-teal);
}

.vessel-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  font-size: 0.9rem;
}

.vessel-detail {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 0.8rem;
  color: var(--text-medium);
  margin-bottom: 0.25rem;
}

/* Weather Widget */
.weather-widget {
  background-color: var(--text-light);
  border-radius: var(--border-radius);
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  box-shadow: var(--card-shadow);
}

.weather-current {
  display: flex;
  align-items: center;
}

.weather-icon {
  font-size: 2.5rem;
  margin-right: 1.25rem;
  color: var(--primary-teal);
}

.weather-info h4 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  font-family: 'Blinker', sans-serif;
}

.weather-details {
  display: grid;
  grid-template-columns: repeat(3, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 0.75rem;
}

.weather-detail {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.weather-forecast {
  display: grid;
  grid-template-columns: repeat(5, minmax(90px, 1fr));
  gap: 0.75rem;
  margin-top: 1rem;
}

.forecast-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem 0.9rem;
  min-width: 90px;
  background-color: #f8fafc;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
  transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
}

.forecast-day:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0,0,0,0.05);
}

.forecast-day .day-name {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--primary-teal);
}

.forecast-day .weather-icon { font-size: 1.5rem; margin: 0 0 0.25rem 0; }

.forecast-day .temp {
  font-size: 0.9rem;
  font-weight: 500;
}

.forecast-day .wind { font-size: 0.8rem; color: var(--text-medium); }

/* Segmented period toggle (dashboard header) */
.period-toggle {
  background: #f3f4f6;
  border-radius: 999px;
  padding: 2px;
  position: relative;
}
.period-toggle .toggle-btn {
  border: none;
  padding: 4px 10px;
  font-size: 0.8rem;
  border-radius: 999px;
  background: transparent;
  cursor: pointer;
  color: #374151;
}
.period-toggle .toggle-btn.active {
  background: #ffffff;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

/* Sliding thumb element for the segmented control */
.period-toggle .toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: calc(50% - 4px);
  height: calc(100% - 4px);
  background: #ffffff;
  border-radius: 999px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  transition: transform 200ms ease;
  pointer-events: none;
}

/* Move slider to the right when month is active */
.period-toggle[data-active="month"] .toggle-slider {
  transform: translateX(100%);
}

/* Loading indicator */
.forecast-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 2rem 0;
  color: var(--text-medium);
}

.forecast-loading i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--primary-teal);
}

/* Impact indicators */
.impact-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
  vertical-align: middle;
}

.impact-low {
  background-color: var(--success-green);
}

.impact-moderate {
  background-color: var(--warning-amber);
}

.impact-high {
  background-color: var(--error-red);
}

.impact-none {
  background-color: #ccc;
}

/* Weather chips */
.chip {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 999px;
  background: #eef2f7;
  color: #374151;
  font-size: 0.8rem;
}
.chip i { color: var(--primary-teal); }
.chip.warn { background: #fff7ed; color: #9a3412; }
.chip.warn i { color: #ea580c; }
.chip.danger { background: #fee2e2; color: #991b1b; }
.chip.danger i { color: #dc2626; }

.forecast-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  width: 100%;
  color: var(--text-medium);
}

.forecast-loading i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--primary-teal);
}

.operational-impact-summary {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--background-medium);
}

.operational-impact-summary h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  color: var(--text-dark);
  font-family: 'Blinker', sans-serif;
}

.impact-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.impact-label {
  color: var(--text-medium);
}

.impact-value {
  font-weight: 500;
  color: var(--text-dark);
}

/* Terminal Visualization */
.terminal-container {
  position: relative;
  width: 100%;
  height: 600px;
  background-color: var(--background-light);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.terminal-visualization {
  width: 100%;
  height: 100%;
}

.terminal-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  z-index: 10;
}

.terminal-tooltip {
  position: absolute;
  background-color: var(--text-light);
  border-radius: var(--border-radius);
  padding: 0.75rem;
  box-shadow: var(--card-shadow);
  z-index: 100;
  max-width: 250px;
  display: none;
}

.terminal-tooltip h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: var(--primary-teal);
}

.terminal-tooltip p {
  margin: 0;
  font-size: 0.85rem;
}

/* Tank Visualization */
.tank {
  stroke: var(--primary-teal);
  stroke-width: 2;
  fill: white;
  transition: fill 0.3s ease;
}

.tank:hover {
  fill: rgba(0, 169, 160, 0.1);
}

.tank-fill {
  fill: var(--primary-teal);
  opacity: 0.7;
}

.tank-label {
  font-size: 10px;
  font-weight: bold;
  text-anchor: middle;
  pointer-events: none;
}

.jetty {
  stroke: var(--accent-orange);
  stroke-width: 3;
  fill: white;
}

.jetty:hover {
  fill: rgba(255, 109, 0, 0.1);
}

.jetty-occupied {
  fill: var(--accent-orange);
  opacity: 0.7;
}

.pipeline {
  stroke: var(--text-medium);
  stroke-width: 2;
  fill: none;
}

.active-pipeline {
  stroke: var(--primary-teal);
  stroke-width: 3;
  stroke-dasharray: 5;
  animation: flow 1s linear infinite;
}

@keyframes flow {
  from {
    stroke-dashoffset: 10;
  }
  to {
    stroke-dashoffset: 0;
  }
}

/* Modal Styling */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 10000;
  overflow: auto;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background-color: var(--text-light);
  margin: 15vh auto;
  max-width: 500px;
  width: 90%;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  position: relative;
  animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.modal-header {
  padding: 1.25rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header h4 {
  margin: 0;
  color: var(--primary-teal);
}

.close-modal {
  font-size: 1.5rem;
  line-height: 1;
  color: var(--text-medium);
  cursor: pointer;
  transition: color 0.2s;
}

.close-modal:hover {
  color: var(--accent-orange);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* Optimization Log */
.optimization-log {
  background: var(--text-light);
  padding: 1rem;
  border-left: 4px solid var(--accent-orange);
  border-radius: var(--border-radius);
  font-family: monospace;
  max-height: 300px;
  overflow-y: auto;
}

.optimization-parameter {
  margin-bottom: 1rem;
}

.optimization-parameter label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--primary-teal);
}

.optimization-parameter input[type="range"] {
  width: 100%;
  margin-bottom: 0.25rem;
}

.parameter-value {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: var(--text-medium);
}

/* Optimization Results */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
}

.metric-card {
  background-color: var(--background-light);
  padding: 1.25rem;
  border-radius: var(--border-radius);
  text-align: center;
  box-shadow: var(--box-shadow);
  transition: transform var(--transition-speed) ease;
}

.metric-card:hover {
  transform: translateY(-3px);
}

.metric-value {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--primary-teal);
  font-family: 'Blinker', sans-serif;
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 0.9rem;
  color: var(--text-medium);
}

.result-section {
  margin-bottom: 2rem;
}

.result-section h4 {
  color: var(--primary-teal);
  margin-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 0.5rem;
}

.chart-container {
  min-height: 200px;
  position: relative;
  margin-top: 1rem;
}

.utilization-chart {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.chart-bar-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.bar-label {
  width: 60px;
  font-weight: 500;
  text-align: right;
}

.bar-wrapper {
  flex: 1;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  overflow: hidden;
}

.bar {
  height: 100%;
  background-color: var(--primary-teal);
  border-radius: 10px;
  transition: width 0.5s ease;
}

.bar-value {
  width: 60px;
  font-size: 0.85rem;
  color: var(--text-medium);
  padding-left: 0.5rem;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--text-medium);
  font-style: italic;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--border-radius);
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background-color: var(--background-light);
  border-radius: var(--border-radius);
  padding: 0.75rem;
  margin-top: 0.5rem;
  font-family: monospace;
  font-size: 0.85rem;
}

.log-entry {
  padding: 0.25rem 0;
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.log-time {
  color: var(--text-medium);
  margin-right: 1rem;
  white-space: nowrap;
}

.status-message {
  padding: 0.75rem;
  border-radius: var(--border-radius);
  background-color: var(--background-light);
  margin-bottom: 1rem;
}

.status-message.running {
  background-color: rgba(0, 169, 160, 0.1);
  color: var(--primary-teal);
}

.status-message.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-green);
}

.status-message {
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  font-weight: 500;
}

.status-message i {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.status-message.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-green);
}

.status-message.error {
  background-color: rgba(229, 57, 53, 0.1);
  color: var(--error-red);
}

/* Form Styles */
.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--primary-teal);
  font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--background-medium);
  border-radius: var(--border-radius);
  background-color: var(--background-light);
  font-family: 'Roboto', sans-serif;
  font-size: 0.95rem;
  transition: border-color var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
}

.form-group input::placeholder {
  color: var(--text-medium);
  opacity: 0.7;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-teal);
  box-shadow: 0 0 0 3px rgba(0, 169, 160, 0.2);
}

.form-group input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.form-group input[type="number"]::-webkit-outer-spin-button,
.form-group input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group.half {
  flex: 1;
  min-width: 0;
}

/* Form Sections */
.form-section {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--background-medium);
}

.form-section:last-of-type {
  border-bottom: none;
}

.section-title {
  font-size: 1.1rem;
  color: var(--primary-teal);
  margin-bottom: 1.25rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px dashed var(--background-medium);
}

/* Input with tooltip */
.input-with-help {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-help input {
  flex: 1;
  padding-right: 2.5rem;
}

.input-tooltip {
  position: absolute;
  right: 0.75rem;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--background-medium);
  color: var(--primary-teal);
  font-size: 0.9rem;
  cursor: help;
}

.input-tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  right: 0;
  background-color: var(--text-dark);
  color: var(--text-light);
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: 0.85rem;
  white-space: nowrap;
  z-index: 10;
  margin-bottom: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.input-tooltip:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  right: 0.5rem;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid var(--text-dark);
  transform: translateY(6px);
  z-index: 10;
}

/* Cargo Styling */
.cargo-item {
  background-color: var(--background-light);
  border-radius: var(--border-radius);
  padding: 1.25rem;
  margin-bottom: 1.25rem;
  position: relative;
  border: 1px solid var(--background-medium);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.cargo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px dashed var(--background-medium);
}

.cargo-title {
  font-weight: 500;
  color: var(--primary-teal);
}

.cargo-actions {
  margin-top: 1rem;
}

.remove-cargo {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
}

.btn-with-icon {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.form-help-text {
  display: block;
  font-size: 0.85rem;
  color: var(--text-medium);
  margin-top: 0.25rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--background-medium);
}

.form-actions .btn {
  min-width: 120px;
  font-weight: 500;
}

/* Validation */
.form-error {
  color: var(--error-red);
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.input-validation-error {
  border-color: var(--error-red) !important;
}

.field-validation-error {
  color: var(--error-red);
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: block;
}

/* Responsive Design */
@media (max-width: 992px) {
  .container {
    grid-template-areas:
      "header header"
      "sidebar main";
    grid-template-rows: var(--header-height) 1fr;
  }

  .widget-container {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .vessel-list {
    grid-template-columns: repeat(auto-fill, minmax(230px, 1fr));
  }
}

@media (max-width: 768px) {
  .container {
    grid-template-areas:
      "header"
      "main";
    grid-template-columns: 1fr;
  }

  .sidebar {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    width: 260px;
    z-index: 100;
  }

  .schedule-grid {
    overflow-x: auto;
  }

  .weather-widget {
    flex-direction: column;
    align-items: flex-start;
  }

  .weather-current {
    margin-bottom: 1rem;
  }

  .weather-details {
    width: 100%;
    justify-content: space-between;
  }

  .weather-forecast {
    overflow-x: auto;
    width: 100%;
    padding-bottom: 0.5rem;
  }

  .terminal-container {
    height: 400px;
  }
}

@media (max-width: 576px) {
  .main {
    padding: 1rem;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .card-header > * + * {
    margin-top: 0.5rem;
  }

  .user-actions {
    gap: 0.5rem;
  }

  .btn {
    padding: 0.5rem 1rem;
  }

  .vessel-details {
    grid-template-columns: 1fr;
  }
}

/* Status Badge Component */
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-light);
  text-transform: capitalize;
}

/* Vessel Status Styles */
.status-badge.status-scheduled,
.vessel-status.scheduled {
  background-color: #3498db; /* Blue */
}

.status-badge.status-approaching,
.vessel-status.approaching {
  background-color: #9b59b6; /* Purple */
}

.status-badge.status-waiting,
.vessel-status.waiting {
  background-color: #e67e22; /* Orange */
  color: var(--text-dark);
}

.status-badge.status-berthed,
.vessel-status.berthed {
  background-color: #1abc9c; /* Teal */
}

.status-badge.status-loading,
.vessel-status.loading {
  background-color: #2ecc71; /* Green */
}

.status-badge.status-unloading,
.vessel-status.unloading {
  background-color: #2ecc71; /* Green */
}

.status-badge.status-completed,
.vessel-status.completed {
  background-color: #7f8c8d; /* Gray */
}

.status-badge.status-departed,
.vessel-status.departed {
  background-color: #95a5a6; /* Light Gray */
}

/* Assignment Status Styles */
.status-badge.status-planned,
.assignment-status.planned {
  background-color: #3498db; /* Blue */
}

.status-badge.status-active,
.assignment-status.active {
  background-color: #2ecc71; /* Green */
}

.status-badge.status-cancelled,
.assignment-status.cancelled {
  background-color: #e74c3c; /* Red */
}

/* Combined Status Styles for Unscheduled Vessels */
.status-badge.unscheduled {
  border: 2px dashed rgba(0, 0, 0, 0.2);
}

/* Status Legend Styles */
.status-legend-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  font-size: 0.9rem;
  max-width: 320px;
}

.status-legend-toggle {
  background-color: var(--primary-teal);
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s;
}

.status-legend-toggle:hover {
  background-color: var(--dark-teal);
}

.status-legend {
  display: none;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  padding: 15px;
  margin-top: 10px;
  max-height: 70vh;
  overflow-y: auto;
  border: 1px solid #ddd;
}

.status-legend.visible {
  display: block;
}

.legend-section {
  margin-bottom: 15px;
}

.legend-section h4 {
  margin: 0 0 8px 0;
  font-size: 1rem;
  color: var(--text-dark);
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
}

.legend-description {
  margin-left: 10px;
  color: var(--text-medium);
}

/* Dark Mode Adjustments */
.dark-mode .status-legend {
  background-color: var(--background-medium);
  border-color: var(--background-medium);
}

.dark-mode .legend-section h4 {
  color: var(--text-light);
  border-bottom-color: var(--background-medium);
}

.dark-mode .legend-description {
  color: var(--text-medium);
}

/* Terminal Management Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--background-light);
  border: 1px solid var(--background-medium);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: 1rem;
  min-width: 300px;
  max-width: 400px;
  z-index: 1000;
  transform: translateX(100%);
  transition: transform var(--transition-speed) ease;
}

.notification.notification-show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 1rem;
}

.notification-message {
  flex: 1;
  font-size: 0.9rem;
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-medium);
  cursor: pointer;
  font-size: 0.8rem;
  padding: 0;
  transition: color var(--transition-speed) ease;
}

.notification-close:hover {
  color: var(--text-dark);
}

.notification-success {
  border-left: 4px solid var(--success-green);
}

.notification-success .notification-message {
  color: var(--success-green);
}

.notification-error {
  border-left: 4px solid var(--error-red);
}

.notification-error .notification-message {
  color: var(--error-red);
}

.notification-info {
  border-left: 4px solid var(--primary-teal);
}

.notification-info .notification-message {
  color: var(--primary-teal);
}

/* Terminal comparison styles */
.terminal-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.terminal-card {
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
    background: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.terminal-card:hover {
    border-color: var(--primary-teal);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.1);
}

.terminal-card.selected {
    border-color: var(--primary-teal);
    background: #f8fbff;
}

.terminal-card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e1e5e9;
}

.selection-checkbox {
    position: relative;
}

.selection-checkbox input[type="checkbox"] {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #ccc;
    border-radius: 4px;
    position: relative;
    cursor: pointer;
}

.selection-checkbox input[type="checkbox"]:checked {
    background: var(--primary-teal);
    border-color: var(--primary-teal);
}

.selection-checkbox input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    color: white;
    font-size: 14px;
    top: -2px;
    left: 2px;
}

.terminal-name {
    margin: 0;
    color: var(--text-dark);
    font-weight: 600;
    flex: 1;
}

.terminal-code {
    background: #ecf0f1;
    color: #7f8c8d;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.terminal-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 15px;
}

.metric {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.metric-label {
    font-size: 12px;
    color: var(--text-medium);
    font-weight: 500;
}

.metric-value {
    font-weight: 600;
    color: var(--text-dark);
}

.terminal-products, .terminal-certifications {
    margin-bottom: 10px;
}

.terminal-products h5, .terminal-certifications h5 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: var(--text-dark);
    font-weight: 600;
}

.product-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.product-tag {
    background: #e8f4fd;
    color: var(--primary-teal);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.product-tag.more {
    background: #ecf0f1;
    color: #7f8c8d;
}

.cert-count {
    font-size: 13px;
    color: var(--text-medium);
}

/* Comparison results */
.comparison-section {
    margin-bottom: 30px;
}

.comparison-section h4 {
    margin-bottom: 15px;
    color: var(--text-dark);
    font-weight: 600;
}

.comparison-actions {
    display: flex;
    gap: 10px;
}

.summary-metric {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    text-align: center;
}

.summary-metric h5 {
    margin: 0 0 8px 0;
    color: var(--text-medium);
    font-size: 14px;
    font-weight: 500;
}

.metric-value-large {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-teal);
    margin-bottom: 5px;
}

.metric-detail {
    font-size: 12px;
    color: var(--text-medium);
}

.metric-label-cell {
    font-weight: 600;
    color: var(--text-dark);
    background: var(--background-light);
}

/* Charts */
.charts-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.chart-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.chart-card h5 {
    margin: 0 0 15px 0;
    color: var(--text-dark);
    font-weight: 600;
}

.chart-placeholder {
    min-height: 200px;
}

.chart-bars {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.bar-item {
    display: flex;
    align-items: center;
    gap: 15px;
}

.bar-label {
    min-width: 80px;
    font-size: 12px;
    font-weight: 500;
    color: var(--text-dark);
}

.bar-container {
    flex: 1;
    height: 20px;
    background: #ecf0f1;
    border-radius: 10px;
    overflow: hidden;
}

.bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-teal), var(--dark-teal));
    border-radius: 10px;
    transition: width 0.5s ease;
}

.bar-value {
    min-width: 80px;
    text-align: right;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-dark);
}

/* ===== Settings page consolidated styles (from /static/css/style.css) ===== */
/* Save button animation */
.btn-primary.saving { position: relative; pointer-events: none; opacity: 0.8; }
.btn-primary.saving::after { content: ''; position: absolute; top: 50%; left: 50%; width: 20px; height: 20px; margin: -10px 0 0 -10px; border: 2px solid rgba(255, 255, 255, 0.3); border-radius: 50%; border-top-color: #fff; animation: spin 1s linear infinite; }
@keyframes spin { to { transform: rotate(360deg); } }

/* Save status indicator */
.save-status { margin-right: 1rem; font-size: 0.9rem; color: #666; }
.save-status.unsaved { color: #f39c12; }
.save-status.saved { color: #27ae60; }

/* Settings page improvements */
.settings-section { padding: 1.5rem; }
.setting-description { font-size: 0.85rem; color: #666; margin-top: 0.25rem; margin-left: 1.5rem; }
.api-key-input { position: relative; display: flex; align-items: center; }
.api-key-input input { padding-right: 2.5rem; }
.toggle-password { position: absolute; right: 0.5rem; top: 50%; transform: translateY(-50%); background: none; border: none; color: #666; cursor: pointer; padding: 0; }
.toggle-password:hover { color: #333; }

/* Connection status */
.connection-status { margin-left: 1rem; font-size: 0.9rem; }
.connection-status.success { color: #27ae60; }
.connection-status.error { color: #e74c3c; }

/* Toast notifications */
.toast-container { 
    position: fixed; 
    top: 20px; 
    right: 20px; 
    z-index: 9999; 
    max-width: 350px;
    pointer-events: none;
}

.toast { 
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 12px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    color: #374151;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: auto;
    position: relative;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.fade-out {
    opacity: 0;
    transform: translateX(100%);
}

.toast-icon {
    flex-shrink: 0;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.toast-message {
    flex: 1;
    line-height: 1.4;
}

.toast-close {
    background: transparent;
    border: none;
    cursor: pointer;
    color: #9ca3af;
    padding: 2px;
    font-size: 14px;
    flex-shrink: 0;
    transition: color 0.2s ease;
}

.toast-close:hover {
    color: #6b7280;
}

/* Toast type variations */
.toast-success {
    border-color: #10b981;
    background: #f0fdf4;
}

.toast-success .toast-icon {
    color: #10b981;
}

.toast-error {
    border-color: #ef4444;
    background: #fef2f2;
}

.toast-error .toast-icon {
    color: #ef4444;
}

.toast-warning {
    border-color: #f59e0b;
    background: #fffbeb;
}

.toast-warning .toast-icon {
    color: #f59e0b;
}

.toast-info {
    border-color: #3b82f6;
    background: #eff6ff;
}

.toast-info .toast-icon {
    color: #3b82f6;
}

@keyframes slideIn { 
    from { 
        transform: translateX(100%); 
        opacity: 0; 
    } 
    to { 
        transform: translateX(0); 
        opacity: 1; 
    } 
}

@keyframes fadeOut { 
    from { 
        transform: translateX(0); 
        opacity: 1; 
    } 
    to { 
        transform: translateX(100%); 
        opacity: 0; 
    } 
}
