# Jetty Planner

## Running locally (Docker)

```powershell
# PowerShell
cd C:\Projects\Jettyplanner
docker compose up -d --build
```

Visit http://localhost:7000.

Notes:
- On some systems the command is `docker-compose` instead of `docker compose`.
- Default app port is 7000. Confirm the port is available on your system before binding it. If you need a different port, edit `docker-compose.yml` `ports` (e.g., `"9001:7000"`).
- Uses PostgreSQL exclusively for data persistence. Configure `DATABASE_URL` or individual DB_* environment variables.

## Windows quick start (PowerShell)

### Option A: Docker Desktop (recommended)
Prerequisites:
- Install Docker Desktop for Windows.

Run:
```powershell
cd C:\Projects\Jettyplanner
docker compose up -d --build
```
Then open http://localhost:7000.

To change the port, update `docker-compose.yml` `ports:` mapping (left side is the host port) and rerun the command above.

### Option B: Native Python (no Docker)
Prerequisites:
- Install Python 3.10+ for Windows (e.g., from the Microsoft Store or [python.org](https://www.python.org/downloads/windows/)).

Create a virtual environment and install dependencies:
```powershell
cd C:\Projects\Jettyplanner
py -3.10 -m venv .venv
Set-ExecutionPolicy -Scope CurrentUser RemoteSigned -Force
./.venv/Scripts/Activate.ps1
python -m pip install -U pip
pip install -r requirements.txt
```

Configure environment (either real keys or test mode):
```powershell
# Quick start with test data (no real keys needed)
python .\run.py --production --api --host 127.0.0.1 --port 7000 --test

# Or create a .env file in the repo root with your keys, then run without --test
# .env example:
# AISSTREAM_API_KEY=your_key
# ANTHROPIC_API_KEY=your_key
# API_HOST=127.0.0.1
# API_PORT=7000
# PRODUCTION=true
# Then run:
# python .\run.py --production --api --host 127.0.0.1 --port 7000
```

Open http://localhost:7000 when the server starts. Confirm the port is available before binding; adjust `--port` if needed.

## Known Issues and Fixes

### Smart ETA Calculation Issues (Fixed)

**Problem**: Vessels close to the terminal were showing unrealistic ETAs due to several calculation issues:

1. **Excessive lock delays**: Closed locks added 4 hours of delay, operational locks added 15-75 minutes randomly
2. **Position-agnostic delays**: All vessels got the same lock delays regardless of distance to locks
3. **Random delays**: `Math.random()` caused inconsistent calculations on page refresh
4. **User ETA fallback**: System always chose the later of user ETA vs calculated ETA, even when calculated was more accurate

**Example**: ARGONON vessel at 7.9nm distance (~1 hour travel time) was showing ETA of 21:05 instead of arriving within the hour.

**Solution implemented**:
- Reduced lock wait times to realistic 30-60 minutes maximum
- Made lock delays position-dependent (only applied when vessel will reach locks soon)
- Removed random delays for consistent calculations
- Prioritized calculated ETA when AIS data is recent and reliable
- Added logic to skip lock delays for vessels that won't reach locks for several hours

