/**
 * Nominated Vessels with Smart ETA Tracking
 */

class NominatedVesselsManager {
    constructor() {
        this.vessels = [];
        this.services = {
            ais: { status: 'loading', vessels: 0 },
            tidal: { status: 'loading', level: null },
            locks: { status: 'loading', operational: 0 },
            tracking: { status: 'loading', tracked: 0 }
        };
        this.filters = {
            eta: '',
            geofence: '',
            status: ''
        };
        this.selectedVessel = null;
        this.autoRefreshHandle = null;
        // Toast functionality now handled by unified toast system
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadServicesStatus();
        this.loadNominatedVessels();
        this.startAutoRefresh();
    }
    
    bindEvents() {
        // Toast system is now globally available

        // Refresh button
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.refreshAll();
        });
        
        // AIS status toggle
        document.getElementById('toggle-ais-status').addEventListener('click', () => {
            this.showAISStatus();
        });
        
        // Filter controls
        document.getElementById('apply-filters').addEventListener('click', () => {
            this.applyFilters();
        });
        
        // Filter inputs
        ['eta-filter', 'geofence-filter', 'status-filter'].forEach(id => {
            document.getElementById(id).addEventListener('change', () => {
                this.updateFilters();
            });
        });
        
        // Legend toggle
        const legendToggle = document.querySelector('.status-legend-toggle');
        const legend = document.querySelector('.status-legend');
        
        legendToggle.addEventListener('click', () => {
            legend.classList.toggle('visible');
        });
        
        // Close legend when clicking outside
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.status-legend-container')) {
                legend.classList.remove('visible');
            }
        });
    }

    // Toast functionality now uses the unified toast system
    showToast(message, type = 'info', timeoutMs = 4000) {
        const options = timeoutMs > 0 ? { timeout: timeoutMs } : { persistent: true };
        return showToast(message, type, options);
    }
    
    async loadServicesStatus() {
        try {
            // Check AIS status
            const aisResponse = await fetch('/api/aisstream/status');
            if (aisResponse.ok) {
                const aisData = await aisResponse.json();
                this.services.ais = {
                    status: aisData.connected ? 'connected' : 'warning',
                    vessels: aisData.vessels_tracked || 0,
                    messages: aisData.total_messages_received
                };
            } else {
                this.services.ais = { status: 'error', vessels: 0 };
            }
            
            // Check tracking status
            const trackingResponse = await fetch('/api/tracking/summary');
            if (trackingResponse.ok) {
                const trackingData = await trackingResponse.json();
                this.services.tracking = {
                    status: 'connected',
                    tracked: trackingData.total_tracked || 0,
                    geofences: trackingData.geofence_counts || {}
                };
            } else {
                this.services.tracking = { status: 'error', tracked: 0 };
            }
            
            // Check tidal service
            try {
                const tidalResponse = await fetch('/api/tidal/current?station=VLISSGN');
                if (tidalResponse.ok) {
                    const tidalData = await tidalResponse.json();
                    this.services.tidal = {
                        status: 'connected',
                        level: tidalData.water_level,
                        station: tidalData.station
                    };
                } else {
                    this.services.tidal = { status: 'warning', level: null };
                }
            } catch (error) {
                this.services.tidal = { status: 'error', level: null };
            }
            
            // Check locks status
            try {
                const lockCodes = ['TNZN', 'HANS', 'KREK']; // Terneuzen, Hansweert, Kreekrak
                const results = await Promise.allSettled(
                    lockCodes.map(code => fetch(`/api/locks/status/${code}`))
                );
                let operationalCount = 0;
                let total = 0;
                for (const r of results) {
                    if (r.status === 'fulfilled' && r.value.ok) {
                        total++;
                        const data = await r.value.json();
                        const status = (data.status || '').toLowerCase();
                        if (status === 'open' || status === 'scheduled') operationalCount++;
                        // Update lock markers on map when available
                        try {
                            if (window.smartEtaMap && window.smartEtaMap.updateLocks) {
                                window.smartEtaMap.updateLocks([data]);
                            }
                        } catch (_) { /* ignore */ }
                    }
                }
                this.services.locks = {
                    status: operationalCount > 0 ? 'connected' : (total > 0 ? 'warning' : 'error'),
                    operational: operationalCount,
                    total: total || lockCodes.length
                };
            } catch (error) {
                this.services.locks = { status: 'error', operational: 0 };
            }
            
            this.updateServicesDisplay();
            
        } catch (error) {
            console.error('Error loading services status:', error);
            this.updateServicesDisplay();
        }
    }
    
    updateServicesDisplay() {
        // Update AIS status
        const aisElement = document.getElementById('ais-status');
        aisElement.className = `service-status ${this.services.ais.status}`;
        aisElement.querySelector('.status-text').textContent = 
            this.services.ais.status === 'connected' ? 
                `${this.services.ais.vessels} vessels tracked` :
                this.services.ais.status === 'warning' ? 'Connecting...' : 'Offline';
        
        // Update tidal status
        const tidalElement = document.getElementById('tidal-status');
        tidalElement.className = `service-status ${this.services.tidal.status}`;
        tidalElement.querySelector('.status-text').textContent = 
            this.services.tidal.level ? 
                `${this.services.tidal.level.toFixed(2)}m` :
                this.services.tidal.status === 'warning' ? 'Limited data' : 'Offline';
        
        // Update locks status
        const locksElement = document.getElementById('locks-status');
        locksElement.className = `service-status ${this.services.locks.status}`;
        locksElement.querySelector('.status-text').textContent = 
            `${this.services.locks.operational}/${this.services.locks.total} operational`;
        
        // Update tracking status
        const trackingElement = document.getElementById('tracking-status');
        trackingElement.className = `service-status ${this.services.tracking.status}`;
        trackingElement.querySelector('.status-text').textContent = 
            `${this.services.tracking.tracked} ships tracked`;
    }
    
    async loadNominatedVessels() {
        let fetchedVessels = [];
        try {
            // Get all vessels with status indicating they're nominated/en route
            const response = await fetch('/api/vessels?status=EN_ROUTE&status=APPROACHING&status=ARRIVED&status=WAITING');
            if (!response.ok) {
                throw new Error('Failed to load vessels');
            }
            fetchedVessels = await response.json();
        } catch (error) {
            console.error('Error fetching nominated vessels:', error);
            this.showToast('Failed to load nominated vessels', 'error');
            return;
        }

        // Filter only vessels that have been nominated (have ETA and are not yet docked)
        this.vessels = (fetchedVessels || []).filter(vessel => 
            vessel.eta && !['DOCKED', 'DEPARTED'].includes(vessel.status)
        );

        // Ensure all nominated vessels with MMSI are tracked so they show on maps/dashboard
        try {
            await this.ensureTrackedForNominated();
        } catch (e) {
            console.warn('Auto-tracking nominated vessels failed (non-fatal):', e);
        }

        // Enhance each vessel with smart ETA calculation (non-fatal per vessel)
        for (const vessel of this.vessels) {
            try {
                await this.enhanceVesselWithSmartETA(vessel);
            } catch (e) {
                console.warn(`Enhancement failed for ${vessel.name} (non-fatal)`, e);
            }
        }

        // Render UI; guard individually so partial errors don't surface as blocking toasts
        try { this.displayVessels(); } catch (e) { console.warn('Display vessels failed', e); }
        try { this.updateVesselCount(); } catch (e) { console.warn('Update count failed', e); }
        try { await this.refreshMapShips(); } catch (e) { console.warn('Map refresh failed', e); }
    }
    
    async enhanceVesselWithSmartETA(vessel) {
        try {
            // Get AIS position if available
            vessel.aisPosition = await this.getVesselAISPosition(vessel);
            
            // Calculate distance and base travel time
            vessel.distanceInfo = this.calculateDistance(vessel);
            
            // Get tidal constraints
            vessel.tidalInfo = await this.getTidalConstraints(vessel);
            
            // Get lock information
            vessel.lockInfo = await this.getLockConstraints(vessel);
            
            // Calculate smart ETA using standardized fields
            const calculatedETA = this.calculateSmartETA(vessel);
            if (calculatedETA) {
                vessel.calculated_eta = calculatedETA.toISOString();
                vessel.eta_source = 'ais_calculated';
                vessel.eta_confidence = this.calculateETAConfidence(vessel);
            }

            // Backward compatibility - keep smartETA for existing UI code
            vessel.smartETA = calculatedETA || (vessel.eta ? new Date(vessel.eta) : new Date());

            // Determine geofence zone
            vessel.geofenceZone = this.determineGeofenceZone(vessel);

        } catch (error) {
            console.error(`Error enhancing vessel ${vessel.name}:`, error);
            // Set fallback values using standardized fields
            vessel.calculated_eta = vessel.eta;
            vessel.eta_source = 'user';
            vessel.eta_confidence = 30; // Low confidence due to error
            vessel.smartETA = vessel.eta ? new Date(vessel.eta) : new Date();
            vessel.geofenceZone = 'unknown';
        }
    }
    
    async getVesselAISPosition(vessel) {
        if (!vessel.mmsi) return null;
        
        try {
            // Try to get vessel from tracking service
            const response = await fetch(`/api/tracking/ships`);
            if (response.ok) {
                const payload = await response.json();
                const ships = payload.ships || [];
                const trackedShip = ships.find(ship => String(ship.mmsi) === String(vessel.mmsi));
                
                if (trackedShip && trackedShip.current_position) {
                    return {
                        latitude: trackedShip.current_position.latitude,
                        longitude: trackedShip.current_position.longitude,
                        speed: trackedShip.current_position.speed,
                        course: trackedShip.current_position.course,
                        timestamp: trackedShip.current_position.timestamp,
                        source: 'tracking'
                    };
                }
            }
            
            // Fallback to direct AIS search; request bypass of radius filter for explicit MMSI
            const searchResponse = await fetch(`/api/vessels/search?query=${vessel.mmsi}&wait_for_data=true&disable_radius_filter=true`);
            if (searchResponse.ok) {
                const searchResults = await searchResponse.json();
                const aisVessel = searchResults.vessels.find(v => v.mmsi === vessel.mmsi);
                
                if (aisVessel && aisVessel.position) {
                    return {
                        latitude: aisVessel.position.latitude,
                        longitude: aisVessel.position.longitude,
                        speed: aisVessel.position.speed || 0,
                        course: aisVessel.position.course || 0,
                        timestamp: aisVessel.last_update,
                        source: 'ais'
                    };
                }
            }
            
        } catch (error) {
            console.warn(`Could not get AIS position for ${vessel.name}:`, error);
        }
        
        return null;
    }
    
    calculateDistance(vessel) {
        // Terminal coordinates (EVOS Terneuzen)
        const terminalLat = 51.34543250288062;
        const terminalLon = 3.751466718019277;
        
        if (!vessel.aisPosition) {
            return {
                distance: null,
                estimatedDistance: 100, // Default fallback
                baseTravelTime: null
            };
        }
        
        const distance = this.calculateHaversineDistance(
            vessel.aisPosition.latitude,
            vessel.aisPosition.longitude,
            terminalLat,
            terminalLon
        );
        
        // Add waterway factor (vessels don't travel in straight lines)
        const waterwayDistance = distance * 1.3; // 30% longer via waterways
        
        // Calculate base travel time
        const speed = vessel.aisPosition.speed || 8; // knots, fallback to 8 knots
        const baseTravelTime = waterwayDistance / speed; // hours
        
        return {
            straightDistance: distance,
            distance: waterwayDistance,
            baseTravelTime: baseTravelTime,
            currentSpeed: speed
        };
    }
    
    calculateHaversineDistance(lat1, lon1, lat2, lon2) {
        const R = 3440.065; // Nautical miles (1 nautical mile = 1.852 km)
        const dLat = this.toRadians(lat2 - lat1);
        const dLon = this.toRadians(lon2 - lon1);
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
                  Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }
    
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
    
    async getTidalConstraints(vessel) {
        try {
            const response = await fetch('/api/tidal/current?station=VLISSGN');
            if (response.ok) {
                const tidalData = await response.json();
                
                // Determine if current conditions are safe for vessel draft
                const safeNavigation = vessel.draft <= (tidalData.water_level - 1.0); // 1m safety margin
                
                return {
                    currentLevel: tidalData.water_level,
                    safeNavigation: safeNavigation,
                    requiredLevel: vessel.draft + 1.0,
                    delayHours: safeNavigation ? 0 : this.estimateTidalDelay(vessel.draft, tidalData.water_level),
                    station: tidalData.station
                };
            }
        } catch (error) {
            console.warn('Could not get tidal data:', error);
        }
        
        return {
            currentLevel: null,
            safeNavigation: true, // Assume safe if no data
            delayHours: 0
        };
    }
    
    estimateTidalDelay(vesselDraft, currentLevel) {
        const requiredLevel = vesselDraft + 1.0;
        if (currentLevel >= requiredLevel) return 0;
        
        // Simplified: assume next high tide in 0-12 hours
        // In reality, we'd use tide predictions
        return Math.random() * 6 + 2; // 2-8 hours delay
    }
    
    async getLockConstraints(vessel) {
        const lockCodes = ['TNZN', 'HANS', 'KREK'];
        const lockInfo = [];
        
        // Check if vessel can pass through locks based on dimensions
        try {
            const compatibilityResponse = await fetch('/api/locks/check-passage', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    length: vessel.length,
                    beam: vessel.beam,
                    draft: vessel.draft
                })
            });
            
            if (compatibilityResponse.ok) {
                const compatibility = await compatibilityResponse.json();
                // Support both old (compatibility.locks) and current (compatibility.lock_compatibility) shapes
                const locksObject = compatibility.locks || compatibility.lock_compatibility || {};

                for (const [lockKey, value] of Object.entries(locksObject)) {
                    const canPass = typeof value === 'boolean' ? value : !!value?.can_pass;
                    let operational = false;
                    let waitTime = 0;

                    try {
                        // lockKey may be a short code (e.g. TNZN) or a name key (e.g. TERNEUZEN).
                        const statusResponse = await fetch(`/api/locks/status/${lockKey}`);
                        if (statusResponse.ok) {
                            const status = await statusResponse.json();
                            // Backend returns status as string ("open", "closed", etc.)
                            operational = (status.status === 'open');
                            // Realistic wait times based on lock status
                            waitTime = operational ? 20 : 45; // 20 min if operational, 45 min if not
                        }
                    } catch (e) {
                        waitTime = 30; // Default 30 min wait
                    }

                    lockInfo.push({
                        code: lockKey,
                        canPass: canPass,
                        operational: operational,
                        waitTime: waitTime // minutes
                    });
                }
            }
        } catch (error) {
            console.warn('Could not check lock compatibility:', error);
        }
        
        // Calculate total lock delay - only apply if vessel will reach locks within reasonable timeframe
        const totalDelayMinutes = lockInfo
            .filter(lock => lock.canPass && this.shouldApplyLockDelay(vessel, lock))
            .reduce((total, lock) => total + lock.waitTime, 0);
        
        return {
            locks: lockInfo,
            totalDelayHours: totalDelayMinutes / 60,
            passable: lockInfo.every(lock => lock.canPass)
        };
    }

    shouldApplyLockDelay(vessel, lock) {
        // Only apply lock delays if vessel will reach locks within 6 hours
        // This prevents distant vessels from getting unrealistic delays

        if (!vessel.distanceInfo || !vessel.distanceInfo.baseTravelTime) {
            // If we don't have travel time info, apply delays conservatively
            return true;
        }

        // Assume locks are roughly halfway to the terminal for most vessels
        // In reality, you'd want to calculate actual distance to each lock
        const timeToReachLocks = vessel.distanceInfo.baseTravelTime * 0.7; // 70% of journey
        const maxDelayThreshold = 6; // hours

        // Only apply lock delays if vessel will reach locks within threshold
        return timeToReachLocks <= maxDelayThreshold;
    }

    calculateSmartETA(vessel) {
        // Use standardized ETA field names
        const userETA = vessel.eta ? new Date(vessel.eta) : null;

        // Calculate theoretical arrival based on current position
        let theoreticalArrival;
        if (vessel.distanceInfo && vessel.distanceInfo.baseTravelTime) {
            theoreticalArrival = new Date(Date.now() + vessel.distanceInfo.baseTravelTime * 60 * 60 * 1000);
        } else if (userETA) {
            theoreticalArrival = new Date(userETA);
        } else {
            // No ETA data available
            return null;
        }

        // Add tidal delay
        if (vessel.tidalInfo && vessel.tidalInfo.delayHours > 0) {
            theoreticalArrival.setTime(theoreticalArrival.getTime() + vessel.tidalInfo.delayHours * 60 * 60 * 1000);
        }

        // Add lock delay
        if (vessel.lockInfo && vessel.lockInfo.totalDelayHours > 0) {
            theoreticalArrival.setTime(theoreticalArrival.getTime() + vessel.lockInfo.totalDelayHours * 60 * 60 * 1000);
        }

        // Smart ETA logic: prioritize calculated ETA when AIS data is recent and reliable
        if (userETA && theoreticalArrival) {
            // Check if AIS data is recent (within 30 minutes) and vessel has position data
            const hasRecentAIS = vessel.aisPosition &&
                vessel.aisPosition.timestamp &&
                (Date.now() - new Date(vessel.aisPosition.timestamp).getTime()) < (30 * 60 * 1000);

            if (hasRecentAIS && vessel.distanceInfo && vessel.distanceInfo.baseTravelTime) {
                // AIS data is fresh and we have good distance calculations - trust calculated ETA
                return theoreticalArrival;
            } else {
                // AIS data is stale or missing - use the later of user ETA or calculated ETA as safety measure
                return theoreticalArrival > userETA ? theoreticalArrival : userETA;
            }
        }

        return theoreticalArrival;
    }

    calculateETAConfidence(vessel) {
        /**
         * Calculate confidence score for ETA prediction based on data quality.
         * Returns a score from 0-100.
         */
        let confidence = 50; // Base confidence

        if (!vessel.aisPosition) {
            return 20; // Very low confidence without position
        }

        // Increase confidence based on data quality
        if (vessel.aisPosition.speed > 0) {
            confidence += 20; // Have speed data
        }

        if (vessel.distanceInfo && vessel.distanceInfo.distance < 50) {
            confidence += 15; // Close to terminal, more predictable
        }

        if (vessel.aisPosition.course !== null && vessel.aisPosition.course !== undefined) {
            confidence += 10; // Have course data
        }

        // Decrease confidence for complex routing
        if (vessel.lockInfo && vessel.lockInfo.totalDelayHours > 0) {
            confidence -= 15; // Lock delays are unpredictable
        }

        if (vessel.tidalInfo && vessel.tidalInfo.delayHours > 0) {
            confidence -= 10; // Tidal constraints add uncertainty
        }

        // Ensure confidence stays within bounds
        return Math.max(10, Math.min(95, confidence));
    }

    determineGeofenceZone(vessel) {
        if (!vessel.aisPosition) return 'unknown';
        
        const distance = vessel.distanceInfo ? vessel.distanceInfo.distance : null;
        if (!distance) return 'unknown';
        
        if (distance < 2) return 'terminal';
        if (distance < 10) return '2hour';
        if (distance < 20) return '4hour';
        return 'tracked';
    }
    
    displayVessels() {
        const tbody = document.getElementById('nominated-vessels-tbody');
        tbody.innerHTML = '';
        
        if (this.vessels.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center">
                        <i class="fas fa-ship"></i> No nominated vessels found
                    </td>
                </tr>
            `;
            return;
        }
        
        // Apply filters
        const filteredVessels = this.applyCurrentFilters();
        
        filteredVessels.forEach(vessel => {
            const row = this.createVesselRow(vessel);
            tbody.appendChild(row);
        });
        
        // Add click handlers
        tbody.querySelectorAll('.view-eta-details').forEach(button => {
            button.addEventListener('click', (e) => {
                const vesselId = e.target.closest('button').dataset.vesselId;
                const vessel = this.vessels.find(v => v.id == vesselId);
                if (vessel) {
                    this.showVesselETADetails(vessel);
                }
            });
        });
    }
    
    createVesselRow(vessel) {
        const row = document.createElement('tr');
        
        // Format times using standardized ETA fields
        const userETA = vessel.eta ? new Date(vessel.eta).toLocaleString('nl-NL', {
            day: '2-digit', month: '2-digit', year: 'numeric',
            hour: '2-digit', minute: '2-digit'
        }) : 'Not set';

        const calculatedETA = vessel.calculated_eta ? new Date(vessel.calculated_eta).toLocaleString('nl-NL', {
            day: '2-digit', month: '2-digit', year: 'numeric',
            hour: '2-digit', minute: '2-digit'
        }) : userETA;

        // Calculate ETA difference (calculated vs user-specified)
        let timeDiff = 0;
        let hoursDiff = 0;
        if (vessel.eta && vessel.calculated_eta) {
            timeDiff = new Date(vessel.calculated_eta).getTime() - new Date(vessel.eta).getTime();
            hoursDiff = timeDiff / (1000 * 60 * 60);
        }

        // Backward compatibility for existing UI
        const smartETA = vessel.smartETA ? vessel.smartETA.toLocaleString('nl-NL', {
            day: '2-digit', month: '2-digit', year: 'numeric',
            hour: '2-digit', minute: '2-digit'
        }) : calculatedETA;
        
        // Position display
        const positionDisplay = vessel.aisPosition ? 
            `<div class="position-display">${vessel.aisPosition.latitude.toFixed(4)}, ${vessel.aisPosition.longitude.toFixed(4)}</div>
             <div class="text-muted">${vessel.aisPosition.speed?.toFixed(1) || 0} kn | ${vessel.aisPosition.course?.toFixed(0) || 0}°</div>` :
            '<span class="text-muted">No AIS data</span>';
        
        // Distance display
        const distanceDisplay = vessel.distanceInfo && vessel.distanceInfo.distance ? 
            `<div class="distance-display">${vessel.distanceInfo.distance.toFixed(1)} nm</div>
             <div class="text-muted">~${(vessel.distanceInfo.baseTravelTime || 0).toFixed(1)}h travel</div>` :
            '<span class="text-muted">Unknown</span>';
        
        // ETA comparison
        const etaComparison = Math.abs(hoursDiff) > 0.5 ?
            `<div class="eta-comparison">
                <span class="eta-original">${userETA}</span>
                <span class="eta-smart">${smartETA}</span>
                <span class="eta-change ${hoursDiff > 0 ? 'later' : 'earlier'}">
                    ${hoursDiff > 0 ? '+' : ''}${hoursDiff.toFixed(1)}h
                </span>
             </div>` :
            `<div class="eta-smart">${smartETA}</div>`;
        
        // Tidal factor
        const tidalDisplay = vessel.tidalInfo ? 
            `<div class="tidal-status ${vessel.tidalInfo.safeNavigation ? 'safe' : 'warning'}">
                <i class="fas fa-water"></i>
                ${vessel.tidalInfo.currentLevel ? vessel.tidalInfo.currentLevel.toFixed(2) + 'm' : 'Unknown'}
                ${vessel.tidalInfo.delayHours > 0 ? `(+${vessel.tidalInfo.delayHours.toFixed(1)}h)` : ''}
             </div>` :
            '<span class="text-muted">No data</span>';
        
        // Lock status
        const lockDisplay = vessel.lockInfo && vessel.lockInfo.locks.length > 0 ?
            `<div class="lock-status ${vessel.lockInfo.passable ? 'operational' : 'warning'}">
                <i class="fas fa-lock"></i>
                ${vessel.lockInfo.locks.filter(l => l.operational).length}/${vessel.lockInfo.locks.length} open
                ${vessel.lockInfo.totalDelayHours > 0 ? `(+${vessel.lockInfo.totalDelayHours.toFixed(1)}h)` : ''}
             </div>` :
            '<span class="text-muted">No data</span>';
        
        // Geofence zone
        const geofenceDisplay = `<span class="geofence-badge ${vessel.geofenceZone}">${vessel.geofenceZone}</span>`;
        
        // Status (Title Case for display)
        const statusClass = vessel.status.toLowerCase();
        const statusLabel = (vessel.status || '').toLowerCase().split('_').map(s => s.charAt(0).toUpperCase() + s.slice(1)).join(' ');
        const statusDisplay = `<span class="vessel-status ${statusClass}">${statusLabel}</span>`;
        
        row.innerHTML = `
            <td>
                <div><strong>${vessel.name}</strong></div>
                <div class="text-muted">${vessel.type} | ${vessel.length}m</div>
                ${vessel.mmsi ? `<div class="text-muted">MMSI: ${vessel.mmsi}</div>` : ''}
            </td>
            <td>${positionDisplay}</td>
            <td>${distanceDisplay}</td>
            <td>${userETA}</td>
            <td>${etaComparison}</td>
            <td>${tidalDisplay}</td>
            <td>${lockDisplay}</td>
            <td>${geofenceDisplay}</td>
            <td>${statusDisplay}</td>
            <td>
                <button class="btn btn-sm btn-info view-eta-details" data-vessel-id="${vessel.id}">
                    <i class="fas fa-chart-line"></i>
                </button>
                <button class="btn btn-sm btn-primary" onclick="window.open('/tracking?vessel=${vessel.mmsi || vessel.id}', '_blank')">
                    <i class="fas fa-route"></i>
                </button>
            </td>
        `;
        
        return row;
    }
    
    applyCurrentFilters() {
        return this.vessels.filter(vessel => {
            // ETA filter
            if (this.filters.eta) {
                const now = new Date();
                const etaTime = vessel.smartETA;
                const hoursDiff = (etaTime - now) / (1000 * 60 * 60);
                
                switch (this.filters.eta) {
                    case 'next_6h': if (hoursDiff > 6) return false; break;
                    case 'next_12h': if (hoursDiff > 12) return false; break;
                    case 'next_24h': if (hoursDiff > 24) return false; break;
                    case 'next_48h': if (hoursDiff > 48) return false; break;
                }
            }
            
            // Geofence filter
            if (this.filters.geofence && vessel.geofenceZone !== this.filters.geofence) {
                return false;
            }
            
            // Status filter
            if (this.filters.status && vessel.status !== this.filters.status) {
                return false;
            }
            
            return true;
        });
    }
    
    updateFilters() {
        this.filters.eta = document.getElementById('eta-filter').value;
        this.filters.geofence = document.getElementById('geofence-filter').value;
        this.filters.status = document.getElementById('status-filter').value;
    }
    
    applyFilters() {
        this.updateFilters();
        this.displayVessels();
    }
    
    updateVesselCount() {
        const filteredCount = this.applyCurrentFilters().length;
        const totalCount = this.vessels.length;
        
        document.getElementById('vessel-count-badge').textContent = 
            `${filteredCount}/${totalCount} vessels`;
        
        document.getElementById('last-update-time').textContent = 
            `Last updated: ${new Date().toLocaleTimeString('nl-NL')}`;
    }
    
    showVesselETADetails(vessel) {
        this.selectedVessel = vessel;
        
        // Update vessel name
        document.getElementById('detail-vessel-name').textContent = vessel.name;
        
        // Position & Movement
        if (vessel.aisPosition) {
            document.getElementById('detail-position').textContent = 
                `${vessel.aisPosition.latitude.toFixed(4)}, ${vessel.aisPosition.longitude.toFixed(4)}`;
            document.getElementById('detail-speed').textContent = 
                `${vessel.aisPosition.speed?.toFixed(1) || 0} knots`;
            document.getElementById('detail-course').textContent = 
                `${vessel.aisPosition.course?.toFixed(0) || 0}°`;
            document.getElementById('detail-last-update').textContent = 
                vessel.aisPosition.timestamp ? new Date(vessel.aisPosition.timestamp).toLocaleString('nl-NL') : 'Unknown';
        } else {
            document.getElementById('detail-position').textContent = 'No AIS data available';
            document.getElementById('detail-speed').textContent = 'Unknown';
            document.getElementById('detail-course').textContent = 'Unknown';
            document.getElementById('detail-last-update').textContent = 'No updates';
        }
        
        if (vessel.distanceInfo) {
            document.getElementById('detail-distance').textContent = 
                `${vessel.distanceInfo.distance?.toFixed(1) || 'Unknown'} nautical miles`;
        }
        
        // ETA Breakdown
        document.getElementById('detail-original-eta').textContent = 
            new Date(vessel.eta).toLocaleString('nl-NL');
        document.getElementById('detail-base-time').textContent = 
            vessel.distanceInfo?.baseTravelTime ? `${vessel.distanceInfo.baseTravelTime.toFixed(1)} hours` : 'Unknown';
        document.getElementById('detail-tidal-delay').textContent = 
            vessel.tidalInfo?.delayHours ? `${vessel.tidalInfo.delayHours.toFixed(1)} hours` : 'None';
        document.getElementById('detail-lock-delay').textContent = 
            vessel.lockInfo?.totalDelayHours ? `${vessel.lockInfo.totalDelayHours.toFixed(1)} hours` : 'None';
        document.getElementById('detail-smart-eta').textContent = 
            vessel.smartETA.toLocaleString('nl-NL');
        
        // Tidal Information
        if (vessel.tidalInfo) {
            document.getElementById('detail-water-level').textContent = 
                vessel.tidalInfo.currentLevel ? `${vessel.tidalInfo.currentLevel.toFixed(2)}m` : 'Unknown';
            document.getElementById('detail-vessel-draft').textContent = 
                `${vessel.draft.toFixed(2)}m`;
            document.getElementById('detail-navigation-safe').textContent = 
                vessel.tidalInfo.safeNavigation ? 'Safe' : 'Restricted';
            document.getElementById('detail-next-high-tide').textContent = 
                'Calculating...'; // Would implement tide predictions
        }
        
        // Lock Information
        this.displayLockDetails(vessel.lockInfo);
        
        // Show the details card
        document.getElementById('vessel-eta-details-card').style.display = 'block';
        
        // Scroll to details
        document.getElementById('vessel-eta-details-card').scrollIntoView({ 
            behavior: 'smooth' 
        });

        // Highlight and focus selected vessel on the map
        try {
            if (window.smartEtaMap && vessel.mmsi) {
                window.smartEtaMap.setSelectedShip(vessel.mmsi);
                window.smartEtaMap.focusOnShip(vessel.mmsi, true);
            }
        } catch (e) { /* no-op */ }
    }
    
    displayLockDetails(lockInfo) {
        const container = document.getElementById('lock-details-container');
        
        if (!lockInfo || !lockInfo.locks.length) {
            container.innerHTML = '<p class="text-muted">No lock information available</p>';
            return;
        }
        
        const lockNames = {
            'TNZN': 'Terneuzen',
            'HANS': 'Hansweert', 
            'KREK': 'Kreekrak'
        };
        
        container.innerHTML = lockInfo.locks.map(lock => `
            <div class="detail-row">
                <div class="detail-label">${lockNames[lock.code] || lock.code}:</div>
                <div class="detail-value">
                    <span class="lock-status ${lock.operational ? 'operational' : 'closed'}">
                        ${lock.canPass ? 'Compatible' : 'Too large'} | 
                        ${lock.operational ? 'Operational' : 'Closed'} |
                        ~${Math.round(lock.waitTime)} min wait
                    </span>
                </div>
            </div>
        `).join('');
    }
    
    async refreshAll() {
        // Show loading state
        document.getElementById('refresh-btn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
        
        try {
            await Promise.all([
                this.loadServicesStatus(),
                this.loadNominatedVessels()
            ]);
        } catch (error) {
            console.error('Error during refresh:', error);
            this.showError('Failed to refresh data');
        } finally {
            document.getElementById('refresh-btn').innerHTML = '<i class="fas fa-sync-alt"></i> Refresh ETAs';
        }
    }
    
    async showAISStatus() {
        try {
            const response = await fetch('/api/aisstream/status');
            const data = await response.json();
            
            this.showToast(`AIS Connected: ${data.connected} | Tracked: ${data.vessels_tracked} | Msgs: ${data.total_messages_received || 0}`, 'info', 6000);
        } catch (error) {
            this.showToast('Failed to get AIS status', 'error');
        }
    }
    
    startAutoRefresh() {
        // Ensure only one interval is active
        if (this.autoRefreshHandle) {
            clearInterval(this.autoRefreshHandle);
            this.autoRefreshHandle = null;
        }
        // Auto-refresh every 5 minutes
        this.autoRefreshHandle = setInterval(() => {
            this.loadServicesStatus();
            // Only refresh vessels if no details are open
            if (!document.getElementById('vessel-eta-details-card').style.display || 
                document.getElementById('vessel-eta-details-card').style.display === 'none') {
                this.loadNominatedVessels();
            }
        }, 5 * 60 * 1000);
    }
    
    destroy() {
        if (this.autoRefreshHandle) {
            clearInterval(this.autoRefreshHandle);
            this.autoRefreshHandle = null;
        }
    }
    
    async ensureTrackedForNominated() {
        // Build batch payload for vessels that have MMSI
        const ships = (this.vessels || [])
            .filter(v => v.mmsi)
            .map(v => ({
                mmsi: String(v.mmsi),
                vessel_name: v.name,
                length: v.length,
                beam: v.beam,
                draft: v.draft,
                nomination_id: v.id
            }));
        if (!ships.length) return;
        try {
            await fetch('/api/tracking/track-batch', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ships })
            });
        } catch (e) {
            // Non-fatal; maps still have AIS fallback
            console.warn('Failed to auto-track nominated vessels', e);
        }
    }
    
    async refreshMapShips() {
        try {
            if (!window.smartEtaMap) return;
            const res = await fetch('/api/tracking/ships');
            if (!res.ok) return;
            const data = await res.json();
            window.smartEtaMap.updateShips(data.ships || []);
            // Highlight nominated vessels
            const nominatedMmsi = (this.vessels || []).map(v => v.mmsi).filter(Boolean).map(String);
            if (nominatedMmsi.length && window.smartEtaMap.setNominated) {
                window.smartEtaMap.setNominated(nominatedMmsi);
            }
        } catch (e) {
            console.warn('Failed to refresh map ships after tracking', e);
        }
    }
    
    showError(message) {
        console.error(message);
        this.showToast(message, 'error');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (window.nominatedVesselsManager && typeof window.nominatedVesselsManager.destroy === 'function') {
        window.nominatedVesselsManager.destroy();
    }
    window.nominatedVesselsManager = new NominatedVesselsManager();
});

window.addEventListener('beforeunload', () => {
    if (window.nominatedVesselsManager && typeof window.nominatedVesselsManager.destroy === 'function') {
        window.nominatedVesselsManager.destroy();
    }
});

// Listen for terminal changes
document.addEventListener('terminalChanged', async (event) => {
    console.log('Terminal changed on nominated vessels page:', event.detail);
    // Refresh data without reloading the whole page to avoid loops
    try {
        if (window.nominatedVesselsManager) {
            await window.nominatedVesselsManager.refreshAll();
        }
    } catch (e) {
        console.warn('Failed to refresh after terminal change', e);
    }
});
