"""
Jetty Scheduler using Google OR-Tools

This module implements the optimization model for scheduling vessels to jetties using Google OR-Tools.
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Tuple, Optional, Set

from ortools.sat.python import cp_model
import numpy as np

from src.models.terminal import Terminal, Jetty
from src.models.vessel import VesselBase, VesselType
from src.models.schedule import Schedule, Assignment
from src.integration.openmeteo_api import OpenMeteoApiClient
from src.utils.status_utils import normalize_status
from src.ml.prediction_service import MLPredictionService
from src.ml.models import PredictionRequest, VesselFeatures

logger = logging.getLogger(__name__)


class JettyScheduler:
    """Scheduler for optimizing jetty assignments using Google OR-Tools"""
    
    def __init__(self, terminal: Terminal, vessels: List[VesselBase], 
                 weather_api: Optional[OpenMeteoApiClient] = None,
                 ml_service: Optional[MLPredictionService] = None,
                 horizon_days: int = 14,  # Increased from 7 to 14 days for better scheduling
                 time_granularity_hours: int = 1,
                 force_assign_all: bool = False,
                 use_ml_predictions: bool = True,
                 reserved_intervals_by_jetty: Optional[Dict[str, List[Tuple[datetime, datetime]]]] = None,
                 approach_time_hours: int = 2,
                 free_wait_buffer_hours: int = 1):
        """
        Initialize the scheduler.
        
        Args:
            terminal: The terminal to schedule.
            vessels: The vessels to schedule.
            weather_api: Optional weather API client for weather constraints.
            ml_service: Optional ML prediction service for enhanced time estimates.
            horizon_days: Planning horizon in days.
            time_granularity_hours: Time granularity in hours.
            use_ml_predictions: Whether to use ML predictions for time estimates.
        """
        self.terminal = terminal
        self.vessels = vessels
        self.weather_api = weather_api
        self.ml_service = ml_service or MLPredictionService()
        self.horizon_days = horizon_days
        self.time_granularity_hours = time_granularity_hours
        self.force_assign_all = force_assign_all
        self.use_ml_predictions = use_ml_predictions
        # Reserved intervals keyed by jetty name: list of (start_dt, end_dt)
        self.reserved_intervals_by_jetty = reserved_intervals_by_jetty or {}
        # Call-in modeling parameters
        self.approach_time_hours = max(0, int(approach_time_hours))
        self.free_wait_buffer_hours = max(0, int(free_wait_buffer_hours))
        
        # Calculate planning horizon - ensure timezone-aware
        self.start_time = datetime.now(timezone.utc).replace(minute=0, second=0, microsecond=0)
        self.end_time = self.start_time + timedelta(days=horizon_days)
        self.time_slots = int(horizon_days * 24 / time_granularity_hours)
        
        # Set weights for objective function
        self.weight_throughput = 10.0  # Weight for maximizing throughput
        self.weight_demurrage = 5.0    # Weight for minimizing demurrage
        self.weight_priority = 3.0     # Weight for vessel priority
        self.weight_utilization = 2.0  # Weight for jetty utilization efficiency
        self.weight_weather = 0.0      # Weight for weather-based optimization
        self.weight_contract = 8.0     # Weight for meeting contract terms
    
    def optimize(self) -> Schedule:
        """
        Run the optimization to create a schedule.
        
        Returns:
            A Schedule object with the optimized assignments.
        """
        logger.info(f"Starting optimization with {len(self.vessels)} vessels and {len(self.terminal.jetties)} jetties")
        logger.info(f"Planning horizon: {self.horizon_days} days, time granularity: {self.time_granularity_hours} hours")
        
        # Create the CP-SAT model
        model = cp_model.CpModel()
        
        # Filter vessels that are relevant for scheduling (e.g., with pending operations)
        # Include WAITING status to handle vessels that were previously optimized but need rescheduling
        relevant_vessels = [v for v in self.vessels if normalize_status(v.status) in ["EN_ROUTE", "APPROACHING", "ARRIVED", "WAITING"]]
        
        logger.info(f"Found {len(relevant_vessels)} relevant vessels for scheduling")
        
        # Variables and constraints
        assignment_vars, start_vars, end_vars, interval_vars = self._create_variables(model, relevant_vessels)
        self._add_constraints(model, assignment_vars, start_vars, end_vars, interval_vars, relevant_vessels)
        objective = self._create_objective(model, assignment_vars, start_vars, end_vars, relevant_vessels)
        
        # Set time limit for solver (5 minutes)
        solver = cp_model.CpSolver()
        solver.parameters.max_time_in_seconds = 300.0
        
        # Solve the model
        logger.info("Solving optimization model...")
        status = solver.Solve(model)
        
        # Process the solution
        if status == cp_model.OPTIMAL or status == cp_model.FEASIBLE:
            logger.info(f"Found {'optimal' if status == cp_model.OPTIMAL else 'feasible'} solution")
            schedule = self._build_schedule(solver, assignment_vars, start_vars, end_vars, relevant_vessels)
            logger.info(f"Created schedule with {len(schedule.assignments)} assignments")
            # Attach objective breakdown for explainability
            try:
                schedule.metadata["objective_breakdown"] = self._compute_objective_breakdown(schedule)
            except Exception as e:
                logger.warning(f"Failed to compute objective breakdown: {e}")
            return schedule
        else:
            logger.warning(f"No solution found, status: {solver.StatusName(status)}")
            return Schedule(start_time=self.start_time, end_time=self.end_time)
    
    def _create_variables(self, model: cp_model.CpModel, vessels: List[VesselBase]) -> Tuple[Dict, Dict, Dict, Dict]:
        """
        Create the decision variables for the model.
        
        Args:
            model: The CP-SAT model.
            vessels: List of vessels to schedule.
            
        Returns:
            Tuple of (assignment_vars, start_vars, end_vars) dictionaries.
        """
        assignment_vars = {}  # assignment_vars[v, j] = 1 if vessel v is assigned to jetty j
        start_vars = {}       # start_vars[v, j] = start time slot for vessel v at jetty j
        end_vars = {}         # end_vars[v, j] = end time slot for vessel v at jetty j
        interval_vars = {}    # interval_vars[v, j] = optional interval for vessel v at jetty j
        
        # For each vessel and compatible jetty (use robust compatibility via Terminal.find_compatible_jetties)
        for v_idx, vessel in enumerate(vessels):
            # Normalize product names to terminal keys
            def _normalize_product(p: str) -> str:
                """
                Normalize ML-aligned product types to jetty-compatible product categories.
                
                Maps the 23 specific product types from our ML Feature Alignment work 
                to the exact product categories defined in evos-terminal-business-rules.md Section 2.1
                
                Jetty Products per Business Rules:
                - Jetty 1: hydrocarbons, benzene, acrylonitrile, propylene_oxide  
                - Jetty 2: hydrocarbons, benzene, propylene_oxide
                - Jetty 3: minerals, benzene
                - Jetty 4: minerals (hose only)
                - Jetty 5: minerals  
                - Jetty 6: minerals, butane
                """
                s = (p or "").strip().lower()
                if not s:
                    return s
                
                # HYDROCARBONS - Jetty 1, 2 (Business Rules: "Hydrocarbons/Naphtha" = single category)
                if any(k in s for k in ["naphta", "naphtha", "diesel", "gasoline", "jet", "fuel", "crude", 
                                       "kerosene", "gasoil", "heating oil", "gas condensates", 
                                       "natural gas condensates", "gtl fluid", "gtl saraline",
                                       "chemfeed naphta", "light naphta", "heavy naphta", "naphta frsr", 
                                       "naphta - gasoline", "naphta (hydro,heavy)", "naphtha catalytic reformed",
                                       "lpg", "lng", "gas", "propane"]):
                    return "hydrocarbons"
                
                # BENZENE - Jetty 1, 2, 3  
                if any(k in s for k in ["benzeen e", "benzene heartcut", "benzene"]):
                    return "benzene"
                
                # ACRYLONITRILE - Jetty 1 only (Business Rules Section 9: "Exclusive to Jetty 1")
                if "acrylonitril" in s or "acrylonitrile" in s:
                    return "acrylonitrile"
                
                # PROPYLENE OXIDE - Jetty 1, 2 (Business Rules Section 9: "Jetty 1 or 2")
                if any(k in s for k in ["propyleenoxide", "propylene", "propylene_oxide"]):
                    return "propylene_oxide"
                
                # BUTANE - Jetty 6 only (Business Rules Section 9: "Exclusive to Jetty 6")
                if "butane" in s:
                    return "butane"
                
                # MINERALS - Jetty 3, 4, 5, 6 (Business Rules Section 2.1 & 9)
                # All chemical/oil products that don't fall into specific categories above
                if any(k in s for k in ["mineral", "lub", "lube", "oil", "base oil", "bitumen", 
                                       "asphalt", "wax", "paraffin", "chemical", "sbp 100/140", 
                                       "1-hexeen", "pygas", "developmental polyol", "neste circular"]):
                    return "minerals"
                
                # Unknown products: return normalized string (will likely cause "no compatible jetties")
                return s

            # Build compatibility via terminal business rules; dedupe by jetty id
            compat_by_id: Dict[str, Jetty] = {}
            vessel_products = { _normalize_product(p) for p in vessel.get_cargo_products() }
            try:
                logger.info(f"[Scheduler] Vessel {getattr(vessel,'id','?')} products -> normalized: {list(vessel.get_cargo_products())} -> {list(vessel_products)}")
            except Exception:
                pass
            try:
                vessel_type_str = "seagoing" if vessel.vessel_type == VesselType.TANKER else "inland_barge"
                if vessel_products:
                    for product in vessel_products:
                        try:
                            j_list = self.terminal.find_compatible_jetties(
                                vessel.length,
                                vessel.draft,
                                vessel.deadweight,
                                product,
                                vessel_type_str,
                                connection_size=None
                            )
                            for j in j_list:
                                compat_by_id[getattr(j, 'id', getattr(j, 'name', str(len(compat_by_id))))] = j
                        except Exception:
                            continue
                else:
                    # Fallback to dimension-only compatibility per vessel type
                    candidates = self.terminal.vessel_berths if vessel.vessel_type == VesselType.TANKER else self.terminal.barge_berths
                    for j in candidates:
                        if vessel.is_compatible_with_jetty(j.max_length, j.max_draft, j.max_deadweight):
                            compat_by_id[getattr(j, 'id', getattr(j, 'name', str(len(compat_by_id))))] = j
                try:
                    logger.info(
                        f"[Scheduler] Vessel {getattr(vessel,'id','?')} compatible jetties for products {list(vessel_products)}: "
                        + ", ".join([getattr(j,'name', getattr(j,'id','?')) for j in compat_by_id.values()])
                    )
                except Exception:
                    pass
            except Exception as e:
                logger.warning(f"Failed to compute compatible jetties for vessel {getattr(vessel,'id','?')}: {e}")

            # Create variables for each compatible jetty
            for jetty in compat_by_id.values():
                j_idx = self.terminal.jetties.index(jetty)
                
                # Binary variable for assignment
                assignment_vars[v_idx, j_idx] = model.NewBoolVar(f'assign_v{v_idx}_j{j_idx}')
                
                # Start and end time variables (in time slots)
                start_vars[v_idx, j_idx] = model.NewIntVar(0, self.time_slots - 1, f'start_v{v_idx}_j{j_idx}')
                
                # Estimate total occupation duration using ML predictions if available
                if self.use_ml_predictions:
                    try:
                        # Extract features for ML prediction
                        features = self.ml_service.extract_features(vessel, jetty)
                        prediction_request = PredictionRequest(
                            vessel_id=vessel.id,
                            features=features,
                            jetty_id=jetty.id,
                            override_jetty_selection=True
                        )
                        prediction_response = self.ml_service.predict_for_vessel(prediction_request, self.terminal)
                        
                        if prediction_response.success and prediction_response.predictions.terminal_time:
                            total_occupation_duration = prediction_response.predictions.terminal_time
                            logger.debug(f"Using ML prediction for vessel {vessel.id} on jetty {jetty.id}: {total_occupation_duration}")
                        else:
                            # Use ML service fallback (business-rule inside ML), not manual calc
                            ml_fallback = self.ml_service.predict_times(features)
                            total_occupation_duration = ml_fallback.terminal_time
                            logger.debug(f"ML response unsuccessful; using ML fallback for vessel {vessel.id} on jetty {jetty.id}: {total_occupation_duration}")
                    except Exception as e:
                        logger.warning(f"Error getting ML prediction for vessel {vessel.id}: {e}")
                        # Use ML fallback based on features to avoid manual calculations
                        try:
                            features = features if 'features' in locals() else self.ml_service.extract_features(vessel, jetty)
                            ml_fallback = self.ml_service.predict_times(features)
                            total_occupation_duration = ml_fallback.terminal_time
                        except Exception:
                            # Last resort: keep a conservative default
                            total_occupation_duration = vessel.estimated_operation_time(jetty.max_flow_rate)
                else:
                    # Use traditional estimation
                    total_occupation_duration = vessel.estimated_operation_time(jetty.max_flow_rate)
                
                total_occupation_slots = max(1, int(total_occupation_duration.total_seconds() / (self.time_granularity_hours * 3600)))
                
                end_vars[v_idx, j_idx] = model.NewIntVar(0, self.time_slots - 1, f'end_v{v_idx}_j{j_idx}')

                # Optional interval linking start/end with presence variable
                interval_vars[v_idx, j_idx] = model.NewOptionalIntervalVar(
                    start_vars[v_idx, j_idx],
                    total_occupation_slots,
                    end_vars[v_idx, j_idx],
                    assignment_vars[v_idx, j_idx],
                    f'int_v{v_idx}_j{j_idx}'
                )
        
        return assignment_vars, start_vars, end_vars, interval_vars
    
    def _add_constraints(self, model: cp_model.CpModel, assignment_vars: Dict, 
                        start_vars: Dict, end_vars: Dict, interval_vars: Dict, vessels: List[VesselBase]):
        """
        Add constraints to the model.
        
        Args:
            model: The CP-SAT model.
            assignment_vars: Assignment variables.
            start_vars: Start time variables.
            end_vars: End time variables.
            vessels: List of vessels to schedule.
        """
        # Each vessel must be assigned to at most one jetty
        for v_idx, vessel in enumerate(vessels):
            vessel_assignments = []
            for j_idx, _ in enumerate(self.terminal.jetties):
                if (v_idx, j_idx) in assignment_vars:
                    vessel_assignments.append(assignment_vars[v_idx, j_idx])
            
            if vessel_assignments:  # Only add constraint if vessel has compatible jetties
                if self.force_assign_all:
                    # Exactly one assignment per vessel that has at least one compatible jetty
                    model.Add(sum(vessel_assignments) == 1)
                    
                    # Add a high weight to this constraint in the objective function
                    # to prioritize vessel assignment over other objectives
                    model.Add(sum(vessel_assignments) >= 1).OnlyEnforceIf(model.NewBoolVar(f'force_assign_v{v_idx}'))
                else:
                    model.Add(sum(vessel_assignments) <= 1)  # At most one assignment per vessel
            else:
                # Log warning for vessels with no compatible jetties
                logger.warning(f"Vessel {vessel.id} ({vessel.name}) has no compatible jetties and cannot be assigned")
        
        # No overlapping assignments for each jetty using NoOverlap with optional intervals
        for j_idx, _ in enumerate(self.terminal.jetties):
            jetty_intervals = []
            # Collect candidate intervals for this jetty
            for v_idx, _ in enumerate(vessels):
                if (v_idx, j_idx) in interval_vars:
                    jetty_intervals.append(interval_vars[v_idx, j_idx])

            # Also respect pre-existing reserved intervals for this jetty, if any, by adding fixed intervals
            try:
                jetty = self.terminal.jetties[j_idx]
                reserved_list = self.reserved_intervals_by_jetty.get(getattr(jetty, 'name', ''), [])
                for rs_dt, re_dt in reserved_list:
                    if re_dt <= self.start_time or rs_dt >= self.end_time:
                        continue  # outside horizon
                    # Clip to horizon bounds and convert to slots
                    adj_rs = max(rs_dt, self.start_time)
                    adj_re = min(re_dt, self.end_time)
                    rs = max(0, int((adj_rs - self.start_time).total_seconds() / (self.time_granularity_hours * 3600)))
                    re = max(0, int((adj_re - self.start_time).total_seconds() / (self.time_granularity_hours * 3600)))
                    if rs >= re:
                        continue
                    # Create fixed interval [rs, re)
                    s = model.NewIntVar(rs, rs, f'reserved_start_j{j_idx}_{rs}_{re}')
                    e = model.NewIntVar(re, re, f'reserved_end_j{j_idx}_{rs}_{re}')
                    size = re - rs
                    jetty_intervals.append(model.NewIntervalVar(s, size, e, f'reserved_j{j_idx}_{rs}_{re}'))
            except Exception as e:
                logger.warning(f"Failed to add reserved interval constraints for jetty index {j_idx}: {e}")

            if jetty_intervals:
                model.AddNoOverlap(jetty_intervals)
        
        # Enhanced ETA constraints using standardized ETA fields
        for v_idx, vessel in enumerate(vessels):
            effective_eta = self._get_effective_eta(vessel)
            if effective_eta:
                # Convert EBR (Effective ETA + approach) to time slot
                ebr_dt = effective_eta + timedelta(hours=self.approach_time_hours)
                eta_slot = max(0, int((ebr_dt - self.start_time).total_seconds() /
                                    (self.time_granularity_hours * 3600)))

                # Log ETA constraint application for debugging
                eta_source = getattr(vessel, 'eta_source', 'unknown')
                eta_confidence = getattr(vessel, 'eta_confidence', 50)
                logger.debug(f"Vessel {vessel.id}: ETA constraint at slot {eta_slot} "
                           f"(source: {eta_source}, confidence: {eta_confidence})")

                # Hard constraint: Vessel cannot start before its effective ETA
                for j_idx, _ in enumerate(self.terminal.jetties):
                    if (v_idx, j_idx) in start_vars:
                        model.Add(start_vars[v_idx, j_idx] >= eta_slot).OnlyEnforceIf(
                            assignment_vars[v_idx, j_idx])

    def _get_effective_eta(self, vessel: VesselBase) -> Optional[datetime]:
        """
        Determine the effective ETA to use for constraints based on confidence.

        Uses calculated_eta if available and confidence >= 60, otherwise falls back to user eta.
        This ensures we use the most reliable ETA estimate for optimization.

        Args:
            vessel: Vessel with ETA data

        Returns:
            Effective ETA datetime or None if no ETA available
        """
        calculated_eta = getattr(vessel, 'calculated_eta', None)
        eta_confidence = getattr(vessel, 'eta_confidence', 50)
        user_eta = getattr(vessel, 'eta', None)

        # Use calculated ETA if available and confidence is high enough
        if calculated_eta and eta_confidence >= 60:
            return calculated_eta

        # Fall back to user-specified ETA
        return user_eta

    def calculate_optimal_call_in_time(self, vessel: VesselBase, target_berth_time: datetime) -> Optional[datetime]:
        """
        Calculate optimal call-in time for a vessel based on ETA confidence and terminal capacity.

        This determines when to call in a vessel to arrive at the optimal time, considering:
        - ETA confidence levels
        - Current terminal capacity
        - Approach time requirements
        - Buffer for uncertainty

        Args:
            vessel: Vessel to calculate call-in time for
            target_berth_time: Desired berth assignment time

        Returns:
            Optimal call-in datetime or None if cannot be calculated
        """
        effective_eta = self._get_effective_eta(vessel)
        if not effective_eta:
            return None

        eta_confidence = getattr(vessel, 'eta_confidence', 50)
        eta_source = getattr(vessel, 'eta_source', 'user')

        # Calculate base call-in time (target berth time - approach time)
        base_call_in = target_berth_time - timedelta(hours=self.approach_time_hours)

        # Add uncertainty buffer based on confidence
        if eta_confidence >= 80:
            # High confidence: minimal buffer
            uncertainty_buffer_hours = 0.5
        elif eta_confidence >= 60:
            # Medium confidence: moderate buffer
            uncertainty_buffer_hours = 1.0
        else:
            # Low confidence: larger buffer
            uncertainty_buffer_hours = 2.0

        # Adjust buffer based on ETA source
        if eta_source == 'ais_calculated':
            # AIS-based ETAs are more dynamic, add extra buffer
            uncertainty_buffer_hours += 0.5
        elif eta_source == 'ml_predicted':
            # ML predictions may have systematic bias, add buffer
            uncertainty_buffer_hours += 1.0

        # Calculate optimal call-in time
        optimal_call_in = base_call_in - timedelta(hours=uncertainty_buffer_hours)

        # Ensure call-in time is not earlier than current time
        current_time = datetime.now(timezone.utc)
        if optimal_call_in < current_time:
            optimal_call_in = current_time

        # Ensure call-in allows vessel to arrive by effective ETA
        min_call_in_for_eta = effective_eta - timedelta(hours=self.approach_time_hours + uncertainty_buffer_hours)
        if optimal_call_in < min_call_in_for_eta:
            optimal_call_in = min_call_in_for_eta

        logger.debug(f"Vessel {vessel.id}: Optimal call-in at {optimal_call_in} "
                    f"(confidence: {eta_confidence}, buffer: {uncertainty_buffer_hours}h)")

        return optimal_call_in
    
    def _create_objective(self, model: cp_model.CpModel, assignment_vars: Dict, 
                         start_vars: Dict, end_vars: Dict, vessels: List[VesselBase]) -> int:
        """
        Create the objective function for the model.
        
        Args:
            model: The CP-SAT model.
            assignment_vars: Assignment variables.
            start_vars: Start time variables.
            end_vars: End time variables.
            vessels: List of vessels to schedule.
            
        Returns:
            The objective variable.
        """
        objective_terms = []
        
        # 1. Maximize throughput (total volume handled)
        throughput_terms = []
        for v_idx, vessel in enumerate(vessels):
            vessel_volume = vessel.total_cargo_volume()
            for j_idx, _ in enumerate(self.terminal.jetties):
                if (v_idx, j_idx) in assignment_vars:
                    # Scale by weight
                    throughput_terms.append(
                        assignment_vars[v_idx, j_idx] * int(vessel_volume * self.weight_throughput))
        
        # 2. Enhanced waiting time / demurrage calculation using effective ETA
        waiting_time_terms = []
        for v_idx, vessel in enumerate(vessels):
            effective_eta = self._get_effective_eta(vessel)
            if effective_eta:
                for j_idx, _ in enumerate(self.terminal.jetties):
                    if (v_idx, j_idx) in assignment_vars:
                        # Compute EBR slot using effective ETA and buffer (in slots)
                        ebr_dt = effective_eta + timedelta(hours=self.approach_time_hours)
                        ebr_slot = max(0, int((ebr_dt - self.start_time).total_seconds() / (self.time_granularity_hours * 3600)))
                        buffer_slots = max(0, int(self.free_wait_buffer_hours / self.time_granularity_hours))

                        # raw_wait = start - ebr_slot
                        raw_wait = model.NewIntVar(-self.time_slots, self.time_slots, f'raw_wait_v{v_idx}_j{j_idx}')
                        model.Add(raw_wait == start_vars[v_idx, j_idx] - ebr_slot).OnlyEnforceIf(assignment_vars[v_idx, j_idx])
                        model.Add(raw_wait == 0).OnlyEnforceIf(assignment_vars[v_idx, j_idx].Not())

                        # buffered_wait = max(0, raw_wait - buffer_slots)
                        tmp = model.NewIntVar(-self.time_slots, self.time_slots, f'tmp_wait_v{v_idx}_j{j_idx}')
                        model.Add(tmp == raw_wait - buffer_slots)
                        buffered_wait = model.NewIntVar(0, self.time_slots, f'buf_wait_v{v_idx}_j{j_idx}')
                        model.Add(buffered_wait >= tmp)

                        # Scale by weight (negative because we want to minimize)
                        waiting_time_terms.append(buffered_wait * -int(self.weight_demurrage))

        # 3. NEW: ETA deviation penalties to optimize call-in timing
        eta_deviation_terms = []
        for v_idx, vessel in enumerate(vessels):
            user_eta = getattr(vessel, 'eta', None)
            calculated_eta = getattr(vessel, 'calculated_eta', None)
            eta_confidence = getattr(vessel, 'eta_confidence', 50)

            # Only add deviation penalty if we have both user and calculated ETAs
            if user_eta and calculated_eta and eta_confidence >= 60:
                for j_idx, _ in enumerate(self.terminal.jetties):
                    if (v_idx, j_idx) in assignment_vars:
                        # Calculate preferred ETA slot (user-specified ETA)
                        preferred_ebr_dt = user_eta + timedelta(hours=self.approach_time_hours)
                        preferred_slot = max(0, int((preferred_ebr_dt - self.start_time).total_seconds() /
                                                  (self.time_granularity_hours * 3600)))

                        # Deviation from preferred timing (absolute difference)
                        deviation = model.NewIntVar(0, self.time_slots, f'eta_dev_v{v_idx}_j{j_idx}')

                        # deviation = |start_time - preferred_slot|
                        diff = model.NewIntVar(-self.time_slots, self.time_slots, f'eta_diff_v{v_idx}_j{j_idx}')
                        model.Add(diff == start_vars[v_idx, j_idx] - preferred_slot).OnlyEnforceIf(assignment_vars[v_idx, j_idx])
                        model.Add(diff == 0).OnlyEnforceIf(assignment_vars[v_idx, j_idx].Not())

                        # deviation = max(diff, -diff)
                        model.AddMaxEquality(deviation, [diff, -diff])

                        # Weight deviation penalty by confidence (higher confidence = higher penalty for deviation)
                        deviation_weight = int(self.weight_demurrage * eta_confidence / 100)
                        eta_deviation_terms.append(deviation * -deviation_weight)
        
        # 4. Prioritize vessels with higher priority
        priority_terms = []
        for v_idx, vessel in enumerate(vessels):
            for j_idx, _ in enumerate(self.terminal.jetties):
                if (v_idx, j_idx) in assignment_vars:
                    # Scale by weight
                    priority_terms.append(
                        assignment_vars[v_idx, j_idx] * int(vessel.priority * self.weight_priority))

        # 5. Add special weight for force assignment when enabled
        force_assignment_terms = []
        if self.force_assign_all:
            # Add a very high weight for each assigned vessel to strongly enforce assignments
            for v_idx, vessel in enumerate(vessels):
                vessel_assignment = []
                for j_idx, _ in enumerate(self.terminal.jetties):
                    if (v_idx, j_idx) in assignment_vars:
                        vessel_assignment.append(assignment_vars[v_idx, j_idx])
                
                if vessel_assignment:  # Only if vessel has compatible jetties
                    # Use a weight that is higher than all other weights combined
                    force_weight = 100 * (self.weight_throughput + self.weight_demurrage + self.weight_priority)
                    force_assignment_terms.append(sum(vessel_assignment) * int(force_weight))
        
        # 6. Jetty utilization efficiency (even distribution across jetties)
        utilization_terms = []
        if self.weight_utilization > 0:
            # Count assignments per jetty and penalize imbalance
            jetty_assignments = []
            for j_idx, _ in enumerate(self.terminal.jetties):
                jetty_use = []
                for v_idx, _ in enumerate(vessels):
                    if (v_idx, j_idx) in assignment_vars:
                        jetty_use.append(assignment_vars[v_idx, j_idx])
                if jetty_use:
                    jetty_assignments.append(sum(jetty_use))
            
            # Promote balanced utilization by rewarding each jetty use
            # This encourages spreading vessels across available jetties
            for jetty_assignment_count in jetty_assignments:
                utilization_terms.append(jetty_assignment_count * int(self.weight_utilization))
        
        # 6. Weather-based optimization (basic implementation)
        weather_terms = []
        if self.weight_weather > 0:
            # Simple weather penalty - in a full implementation, this would
            # check weather forecast and penalize assignments during high wind periods
            # For now, we'll implement a placeholder that slightly favors earlier assignments
            # (assuming earlier assignments are less likely to be affected by weather changes)
            for v_idx, vessel in enumerate(vessels):
                for j_idx, _ in enumerate(self.terminal.jetties):
                    if (v_idx, j_idx) in assignment_vars and (v_idx, j_idx) in start_vars:
                        # Create a helper variable for weather penalty to avoid multiplication issues
                        # weather_penalty_var will be 0 if not assigned, or (time_slots - start_slot) if assigned
                        weather_penalty_var = model.NewIntVar(0, self.time_slots, f'weather_penalty_v{v_idx}_j{j_idx}')
                        
                        # If assigned, set weather_penalty_var = time_slots - start_slot
                        model.Add(weather_penalty_var == self.time_slots - start_vars[v_idx, j_idx]).OnlyEnforceIf(assignment_vars[v_idx, j_idx])
                        # If not assigned, set weather_penalty_var = 0
                        model.Add(weather_penalty_var == 0).OnlyEnforceIf(assignment_vars[v_idx, j_idx].Not())
                        
                        # Add the scaled weather penalty to objective
                        weather_terms.append(weather_penalty_var * int(self.weight_weather / 10))
        
        # 7. Optional jetty balancing (legacy - kept for compatibility)
        balancing_terms = []
        try:
            weight_balance = getattr(self, 'weight_jetty_balance', 0.0)
        except Exception:
            weight_balance = 0.0
        if weight_balance and weight_balance > 0:
            for j_idx, _ in enumerate(self.terminal.jetties):
                jetty_use = []
                for v_idx, _ in enumerate(vessels):
                    if (v_idx, j_idx) in assignment_vars:
                        jetty_use.append(assignment_vars[v_idx, j_idx])
                if jetty_use:
                    balancing_terms.append(sum(jetty_use) * int(weight_balance))

        # Combine all terms
        objective_terms.extend(throughput_terms)
        objective_terms.extend(waiting_time_terms)
        objective_terms.extend(eta_deviation_terms)  # NEW: ETA deviation penalties
        objective_terms.extend(priority_terms)
        objective_terms.extend(force_assignment_terms)
        objective_terms.extend(utilization_terms)
        objective_terms.extend(weather_terms)
        objective_terms.extend(balancing_terms)
        
        # Set the objective
        model.Maximize(sum(objective_terms))
        
        return sum(objective_terms)
    
    def _build_schedule(self, solver: cp_model.CpSolver, assignment_vars: Dict, 
                       start_vars: Dict, end_vars: Dict, vessels: List[VesselBase]) -> Schedule:
        """
        Build a Schedule object from the solver results.
        
        Args:
            solver: The CP-SAT solver with solution.
            assignment_vars: Assignment variables.
            start_vars: Start time variables.
            end_vars: End time variables.
            vessels: List of vessels used in optimization.
            
        Returns:
            A Schedule object with assignments.
        """
        schedule = Schedule(start_time=self.start_time, end_time=self.end_time)
        assignment_count = 0
        
        # Process each potential vessel-jetty assignment
        for v_idx, vessel in enumerate(vessels):
            for j_idx, jetty in enumerate(self.terminal.jetties):
                if (v_idx, j_idx) in assignment_vars and solver.Value(assignment_vars[v_idx, j_idx]) == 1:
                    # This vessel is assigned to this jetty
                    start_slot = solver.Value(start_vars[v_idx, j_idx])
                    end_slot = solver.Value(end_vars[v_idx, j_idx])
                    
                    # Convert time slots to datetime
                    start_time = self.start_time + timedelta(hours=start_slot * self.time_granularity_hours)
                    end_time = self.start_time + timedelta(hours=end_slot * self.time_granularity_hours)
                    
                    # Create assignment
                    assignment = Assignment(
                        id=f"A{assignment_count:04d}",
                        jetty=jetty,
                        vessel=vessel,
                        start_time=start_time,
                        end_time=end_time,
                        status="PENDING_APPROVAL"
                    )
                    
                    # Apply ML predictions if enabled
                    if self.use_ml_predictions:
                        try:
                            features = self.ml_service.extract_features(vessel, jetty)
                            prediction_request = PredictionRequest(
                                vessel_id=vessel.id,
                                features=features,
                                jetty_id=jetty.id,
                                override_jetty_selection=True
                            )
                            prediction_response = self.ml_service.predict_for_vessel(prediction_request, self.terminal)
                            
                            if prediction_response.success:
                                assignment.apply_ml_predictions(
                                    prediction_response.predictions,
                                    prediction_response.jetty_id
                                )
                                logger.debug(f"Applied ML predictions to assignment {assignment.id}")
                            else:
                                # Use ML service fallback predictions to avoid losing terminal time
                                try:
                                    fallback_preds = self.ml_service.predict_times(features)
                                    assignment.apply_ml_predictions(fallback_preds, jetty.id)
                                    logger.info(
                                        f"Applied ML fallback predictions to assignment {assignment.id} after validation error: {prediction_response.error_message}"
                                    )
                                except Exception as inner_e:
                                    logger.warning(
                                        f"Failed to apply ML fallback predictions for assignment {assignment.id}: {inner_e}"
                                    )
                        except Exception as e:
                            logger.warning(f"Error applying ML predictions to assignment {assignment.id}: {e}")
                    
                    # Add assignment to schedule
                    schedule.add_assignment(assignment)
                    assignment_count += 1
        
        # Calculate objective value (can be used for comparing schedules)
        schedule.objective_value = solver.ObjectiveValue()
        
        return schedule

    def _compute_objective_breakdown(self, schedule: Schedule) -> Dict:
        """
        Compute an approximate breakdown of the objective function into components for explainability.
        This mirrors the terms in _create_objective using the realized assignment schedule.

        Returns a dict with totals and per-assignment contributions.
        """
        totals = {
            "throughput": 0.0,
            "demurrage": 0.0,   # negative (penalty)
            "priority": 0.0,
            "force_assign": 0.0,
            "balancing": 0.0,
        }

        per_assignment: List[Dict] = []

        # Jetty counts for balancing (soft) if configured
        try:
            weight_balance = getattr(self, 'weight_jetty_balance', 0.0) or 0.0
        except Exception:
            weight_balance = 0.0

        # Precompute per-jetty assignment counts
        jetty_counts: Dict[str, int] = {}
        for a in schedule.assignments:
            jetty_id = getattr(a.jetty, 'id', getattr(a.jetty, 'name', ''))
            jetty_counts[jetty_id] = jetty_counts.get(jetty_id, 0) + 1

        # Force assign term (if enabled) is a constant per assigned vessel
        force_weight = 0.0
        if self.force_assign_all:
            force_weight = 100.0 * (float(self.weight_throughput) + float(self.weight_demurrage) + float(self.weight_priority))

        for a in schedule.assignments:
            vessel = a.vessel
            assignment_score = {
                "assignment_id": getattr(a, 'id', None),
                "vessel_id": getattr(vessel, 'id', None),
                "vessel_name": getattr(vessel, 'name', ''),
                "jetty_id": getattr(a.jetty, 'id', None),
                "jetty_name": getattr(a.jetty, 'name', ''),
                "throughput": 0.0,
                "demurrage": 0.0,
                "priority": 0.0,
                "force_assign": 0.0,
                "reasons": [],
            }

            # Throughput contribution
            try:
                volume = float(vessel.total_cargo_volume())
            except Exception:
                volume = 0.0
            tp = volume * float(self.weight_throughput)
            assignment_score["throughput"] = tp
            totals["throughput"] += tp
            if volume > 0:
                assignment_score["reasons"].append(f"Handles {int(volume):,} m³ cargo")

            # Enhanced demurrage/wait penalty using effective ETA
            demurrage_points = 0.0
            wait_hours_over_buffer = 0.0
            eta_deviation_points = 0.0
            try:
                effective_eta = self._get_effective_eta(vessel)
                if effective_eta:
                    # Calculate demurrage based on effective ETA
                    ebr = effective_eta + timedelta(hours=int(self.approach_time_hours))
                    raw_wait_hours = max(0.0, (a.start_time - ebr).total_seconds() / 3600.0)
                    wait_hours_over_buffer = max(0.0, raw_wait_hours - float(self.free_wait_buffer_hours))
                    demurrage_points = - float(self.weight_demurrage) * wait_hours_over_buffer

                    # Calculate ETA deviation penalty if applicable
                    user_eta = getattr(vessel, 'eta', None)
                    calculated_eta = getattr(vessel, 'calculated_eta', None)
                    eta_confidence = getattr(vessel, 'eta_confidence', 50)

                    if user_eta and calculated_eta and eta_confidence >= 60:
                        # Deviation from user-preferred timing
                        preferred_ebr = user_eta + timedelta(hours=int(self.approach_time_hours))
                        deviation_hours = abs((a.start_time - preferred_ebr).total_seconds() / 3600.0)
                        deviation_weight = float(self.weight_demurrage) * eta_confidence / 100
                        eta_deviation_points = - deviation_weight * deviation_hours

            except Exception:
                demurrage_points = 0.0
                eta_deviation_points = 0.0

            assignment_score["demurrage"] = demurrage_points + eta_deviation_points
            totals["demurrage"] += demurrage_points + eta_deviation_points

            if wait_hours_over_buffer > 0.0:
                assignment_score["reasons"].append(f"Wait beyond buffer: {wait_hours_over_buffer:.1f} h")
            if eta_deviation_points < 0:
                deviation_hours = abs(eta_deviation_points / (float(self.weight_demurrage) * getattr(vessel, 'eta_confidence', 50) / 100))
                assignment_score["reasons"].append(f"ETA deviation: {deviation_hours:.1f} h")

            # Priority
            try:
                prio = int(getattr(vessel, 'priority', 0))
            except Exception:
                prio = 0
            pr_points = prio * float(self.weight_priority)
            assignment_score["priority"] = pr_points
            totals["priority"] += pr_points
            if prio:
                assignment_score["reasons"].append(f"Customer priority {prio}")

            # Force assign pseudo-term
            if force_weight > 0.0:
                assignment_score["force_assign"] = force_weight
                totals["force_assign"] += force_weight

            per_assignment.append(assignment_score)

        # Balancing (soft): proportional to number of assignments per jetty
        if weight_balance and weight_balance > 0.0:
            for count in jetty_counts.values():
                totals["balancing"] += float(weight_balance) * float(count)

        total = sum(totals.values())
        return {
            "totals": totals,
            "total_objective": total,
            "per_assignment": per_assignment,
            "weights": {
                "throughput": float(self.weight_throughput),
                "demurrage": float(self.weight_demurrage),
                "priority": float(self.weight_priority),
                "utilization": float(self.weight_utilization),
                "weather": float(self.weight_weather),
                "force_assign_all": bool(self.force_assign_all),
                "free_wait_buffer_hours": int(self.free_wait_buffer_hours),
                "approach_time_hours": int(self.approach_time_hours),
                "jetty_balance": float(weight_balance),
            }
        }
