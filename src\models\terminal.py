"""
Terminal Model

This module defines the Terminal class and related entities for the petrochemical terminal.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Set, Any
from enum import Enum
from datetime import datetime, timedelta


class JettyType(Enum):
    """Type of jetty"""
    VESSEL_BERTH = "vessel_berth"
    BARGE_BERTH = "barge_berth"


class LoadingArmType(Enum):
    """Type of loading arm"""
    GASOLINE = "gasoline"
    GASOIL = "gasoil"
    JETFUEL = "jetfuel"
    BIOFUEL = "biofuel"
    MULTI_PRODUCT = "multi_product"  # Can handle multiple products


class ConnectionSize(Enum):
    """ANSI connection sizes"""
    FOUR_INCH = "4\""
    SIX_INCH = "6\""
    EIGHT_INCH = "8\""
    TEN_INCH = "10\""
    TWELVE_INCH = "12\""


class SecurityLevel(Enum):
    """ISPS security levels"""
    ONE = "level_1"  # Normal security
    TWO = "level_2"  # Heightened security
    THREE = "level_3"  # Exceptional security measures


class WeatherRestriction(Enum):
    """Types of weather restrictions"""
    THUNDERSTORM = "thunderstorm"
    WIND_SPEED = "wind_speed"
    MOORING = "mooring"
    ARM_CONNECTION = "arm_connection"
    LOADING = "loading"


@dataclass
class LoadingArm:
    """Loading arm for product transfer"""
    id: str
    name: str
    flow_rate: float  # Flow rate in m³/h
    compatible_products: List[str]  # List of product types this arm can handle
    connection_size: ConnectionSize = ConnectionSize.EIGHT_INCH  # ANSI connection size (default)
    has_vapor_return: bool = False
    vapor_return_size: Optional[ConnectionSize] = None
    requires_nitrogen_purge: bool = False
    is_operational: bool = True
    
    def to_dict(self) -> dict:
        """Convert loading arm to dictionary for serialization"""
        return {
            "id": self.id,
            "name": self.name,
            "flow_rate": self.flow_rate,
            "compatible_products": list(self.compatible_products),
            "connection_size": self.connection_size.value,
            "has_vapor_return": self.has_vapor_return,
            "vapor_return_size": self.vapor_return_size.value if self.vapor_return_size else None,
            "requires_nitrogen_purge": self.requires_nitrogen_purge,
            "is_operational": self.is_operational
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'LoadingArm':
        """Create loading arm from dictionary"""
        vapor_return_size = None
        if data.get("vapor_return_size"):
            vapor_return_size = ConnectionSize(data["vapor_return_size"])
            
        return cls(
            id=data["id"],
            name=data["name"],
            flow_rate=data["flow_rate"],
            compatible_products=data["compatible_products"],
            connection_size=ConnectionSize(data["connection_size"]),
            has_vapor_return=data.get("has_vapor_return", False),
            vapor_return_size=vapor_return_size,
            requires_nitrogen_purge=data.get("requires_nitrogen_purge", False),
            is_operational=data.get("is_operational", True)
        )


@dataclass
class Pump:
    """Pump for transferring products"""
    id: str
    name: str
    flow_rate: float  # Flow rate in m³/h
    compatible_products: List[str]  # List of product types this pump can handle
    is_operational: bool = True
    
    def to_dict(self) -> dict:
        """Convert pump to dictionary for serialization"""
        return {
            "id": self.id,
            "name": self.name,
            "flow_rate": self.flow_rate,
            "compatible_products": list(self.compatible_products),
            "is_operational": self.is_operational
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Pump':
        """Create pump from dictionary"""
        return cls(
            id=data["id"],
            name=data["name"],
            flow_rate=data["flow_rate"],
            compatible_products=data["compatible_products"],
            is_operational=data["is_operational"]
        )


@dataclass
class Tank:
    """Storage tank for products"""
    id: str
    name: str
    capacity: float  # Capacity in m³
    current_volume: float  # Current volume in m³
    product_type: Optional[str] = None  # Type of product stored
    is_floating_roof: bool = False
    is_operational: bool = True
    
    def to_dict(self) -> dict:
        """Convert tank to dictionary for serialization"""
        return {
            "id": self.id,
            "name": self.name,
            "capacity": self.capacity,
            "current_volume": self.current_volume,
            "product_type": self.product_type,
            "is_floating_roof": self.is_floating_roof,
            "is_operational": self.is_operational
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Tank':
        """Create tank from dictionary"""
        return cls(
            id=data["id"],
            name=data["name"],
            capacity=data["capacity"],
            current_volume=data["current_volume"],
            product_type=data.get("product_type"),
            is_floating_roof=data.get("is_floating_roof", False),
            is_operational=data.get("is_operational", True)
        )


@dataclass
class Surveyor:
    """Surveyor for quality control and measurements"""
    id: str
    name: str
    availability: Dict[datetime, bool] = field(default_factory=dict)
    specialization: Optional[Set[str]] = None  # Product specialization if any
    
    def to_dict(self) -> dict:
        """Convert surveyor to dictionary for serialization"""
        return {
            "id": self.id,
            "name": self.name,
            "availability": {k.isoformat(): v for k, v in self.availability.items()},
            "specialization": list(self.specialization) if self.specialization else None
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Surveyor':
        """Create surveyor from dictionary"""
        return cls(
            id=data["id"],
            name=data["name"],
            availability={datetime.fromisoformat(k): v for k, v in data["availability"].items()},
            specialization=set(data["specialization"]) if data.get("specialization") else None
        )


@dataclass
class Jetty:
    """Jetty for loading/unloading vessels"""
    id: str
    name: str
    jetty_type: JettyType
    max_length: float  # Maximum vessel length in meters
    max_draft: float  # Maximum vessel draft in meters
    max_deadweight: float  # Maximum vessel deadweight in tonnes
    loading_arms: List[LoadingArm] = field(default_factory=list)
    connected_tanks: List[str] = field(default_factory=list)  # IDs of connected tanks
    connected_pumps: List[str] = field(default_factory=list)  # IDs of connected pumps
    
    # Operational constraints and safety requirements
    weather_restrictions: Dict[str, Any] = field(default_factory=dict)
    safety_requirements: List[str] = field(default_factory=list)
    security_level: SecurityLevel = SecurityLevel.ONE
    environmental_restrictions: List[str] = field(default_factory=list)
    emergency_equipment: List[str] = field(default_factory=list)
    
    # Vessel type restrictions
    inland_only: bool = False  # True for jetties 2 and 4
    preferred_heading: Optional[str] = None  # "bow_north" or "bow_south"
    
    is_operational: bool = True
    
    @property
    def is_vessel_berth(self) -> bool:
        """Check if this is a vessel berth"""
        return self.jetty_type == JettyType.VESSEL_BERTH
    
    @property
    def is_barge_berth(self) -> bool:
        """Check if this is a barge berth"""
        return self.jetty_type == JettyType.BARGE_BERTH
    
    @property
    def max_flow_rate(self) -> float:
        """Calculate maximum flow rate based on available loading arms"""
        if not self.loading_arms or not any(arm.is_operational for arm in self.loading_arms):
            return 0.0
        return sum(arm.flow_rate for arm in self.loading_arms if arm.is_operational)
    
    def can_handle_product(self, product: str) -> bool:
        """Check if this jetty can handle a specific product"""
        return any(product in arm.compatible_products for arm in self.loading_arms if arm.is_operational)
    
    def can_handle_vessel(self, vessel_length: float, vessel_draft: float, 
                         vessel_deadweight: float, vessel_type: str = "seagoing") -> tuple[bool, str]:
        """
        Check if vessel is compatible with jetty constraints (Rules VC-1 through VC-5)
        
        Args:
            vessel_length: Vessel length in meters
            vessel_draft: Vessel draft in meters  
            vessel_deadweight: Vessel deadweight in tonnes
            vessel_type: "seagoing" or "inland_barge"
            
        Returns:
            Tuple of (is_compatible, reason_if_not)
        """
        # Rule VC-1: Vessel DWT must be within jetty's min-max range
        if vessel_deadweight > self.max_deadweight:
            return False, f"Vessel DWT ({vessel_deadweight}t) exceeds jetty max ({self.max_deadweight}t)"
        
        # Rule VC-2: Vessel length must be within jetty's operational range
        if vessel_length > self.max_length:
            return False, f"Vessel length ({vessel_length}m) exceeds jetty max ({self.max_length}m)"
        
        # Rule VC-4: Vessel draft cannot exceed jetty/harbor maximum
        if vessel_draft > self.max_draft:
            return False, f"Vessel draft ({vessel_draft}m) exceeds jetty max ({self.max_draft}m)"
        
        # Rule VT-1: Inland-only jetties
        if self.inland_only and vessel_type == "seagoing":
            return False, "Jetty is inland-only, cannot handle seagoing vessels"
        
        return True, ""
    
    def check_connection_compatibility(self, vessel_connection_size: str) -> tuple[bool, str]:
        """
        Check if vessel connection size is compatible with jetty arms (Rule PC-2)
        
        Args:
            vessel_connection_size: Vessel's connection size
            
        Returns:
            Tuple of (is_compatible, message)
        """
        compatible_arms = []
        for arm in self.loading_arms:
            if arm.is_operational and arm.connection_size.value == vessel_connection_size:
                compatible_arms.append(arm)
        
        if not compatible_arms:
            available_sizes = [arm.connection_size.value for arm in self.loading_arms if arm.is_operational]
            return False, f"No compatible arms. Available: {available_sizes}, Required: {vessel_connection_size}"
        
        return True, f"Compatible with {len(compatible_arms)} arm(s)"
    
    def check_weather_constraints(self, wind_speed: float, thunderstorm_active: bool = False) -> tuple[bool, str]:
        """
        Check weather-related constraints (Rules W-1 through W-6)
        
        Args:
            wind_speed: Current wind speed in m/s
            thunderstorm_active: Whether thunderstorm is active
            
        Returns:
            Tuple of (can_operate, restriction_reason)
        """
        # Rule W-1: Thunderstorm complete stop
        if thunderstorm_active:
            return False, "Thunderstorm active - all operations suspended"
        
        # Rule W-2: Mooring operations wind limit
        if wind_speed > 17:
            return False, f"Wind speed {wind_speed} m/s exceeds mooring limit (17 m/s)"
        
        # Rule W-3: Arm/hose connection wind limit  
        if wind_speed > 14:
            return False, f"Wind speed {wind_speed} m/s exceeds arm connection limit (14 m/s)"
        
        # Rule W-4: Loading operations wind limit
        if wind_speed > 17:
            return False, f"Wind speed {wind_speed} m/s exceeds loading limit (17 m/s)"
        
        return True, ""
    
    def get_max_flow_rate_for_product(self, product: str, connection_size: str) -> float:
        """
        Get maximum flow rate for a specific product and connection size
        Based on flow rate table in Section 5
        """
        # Flow rate table from business rules (m³/h at different velocities)
        flow_rates = {
            "4\"": {1: 30, 2: 60, 3: 85, 4: 120, 5: 145, 6: 175, 7: 200, 8: 240},
            "6\"": {1: 65, 2: 130, 3: 200, 4: 260, 5: 325, 6: 390, 7: 450, 8: 520},
            "8\"": {1: 120, 2: 240, 3: 350, 4: 460, 5: 580, 6: 700, 7: 820, 8: 920},
            "10\"": {1: 180, 2: 360, 3: 540, 4: 720, 5: 910, 6: 1090, 7: 1270, 8: 1450},
            "12\"": {1: 260, 2: 520, 3: 780, 4: 1040, 5: 1300, 6: 1560, 7: 1820, 8: 2080}
        }
        
        if connection_size not in flow_rates:
            return 0.0
            
        # Use 4 m/s as default operational velocity (reasonable balance)
        operational_velocity = 4
        max_possible = flow_rates[connection_size].get(operational_velocity, 0)
        
        # Check if any operational arm can achieve this rate
        for arm in self.loading_arms:
            if (arm.is_operational and 
                product in arm.compatible_products and 
                arm.connection_size.value == connection_size):
                return min(max_possible, arm.flow_rate)
        
        return 0.0
    
    def to_dict(self) -> dict:
        """Convert jetty to dictionary for serialization"""
        return {
            "id": self.id,
            "name": self.name,
            "jetty_type": self.jetty_type.value,
            "max_length": self.max_length,
            "max_draft": self.max_draft,
            "max_deadweight": self.max_deadweight,
            "loading_arms": [arm.to_dict() for arm in self.loading_arms],
            "connected_tanks": self.connected_tanks,
            "connected_pumps": self.connected_pumps,
            "weather_restrictions": self.weather_restrictions,
            "safety_requirements": self.safety_requirements,
            "security_level": self.security_level.value,
            "environmental_restrictions": self.environmental_restrictions,
            "emergency_equipment": self.emergency_equipment,
            "inland_only": self.inland_only,
            "preferred_heading": self.preferred_heading,
            "is_operational": self.is_operational
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Jetty':
        """Create jetty from dictionary"""
        return cls(
            id=data["id"],
            name=data["name"],
            jetty_type=JettyType(data["jetty_type"]),
            max_length=data["max_length"],
            max_draft=data["max_draft"],
            max_deadweight=data["max_deadweight"],
            loading_arms=[LoadingArm.from_dict(arm) for arm in data["loading_arms"]],
            connected_tanks=data["connected_tanks"],
            connected_pumps=data["connected_pumps"],
            weather_restrictions=data.get("weather_restrictions", {}),
            safety_requirements=data.get("safety_requirements", []),
            security_level=SecurityLevel(data.get("security_level", "level_1")),
            environmental_restrictions=data.get("environmental_restrictions", []),
            emergency_equipment=data.get("emergency_equipment", []),
            inland_only=data.get("inland_only", False),
            preferred_heading=data.get("preferred_heading"),
            is_operational=data.get("is_operational", True)
        )


@dataclass
class Terminal:
    """Petrochemical terminal with jetties, tanks, and other resources"""
    name: str
    jetties: List[Jetty] = field(default_factory=list)
    tanks: List[Tank] = field(default_factory=list)
    pumps: List[Pump] = field(default_factory=list)
    surveyors: List[Surveyor] = field(default_factory=list)
    location: Optional[tuple] = None  # (latitude, longitude)
    
    def __post_init__(self):
        self._validate_terminal()
    
    def _validate_terminal(self):
        """Validate terminal configuration"""
        # Check for at least one jetty
        if not self.jetties:
            raise ValueError("Terminal must have at least one jetty")
        
        # Check jetty type counts
        vessel_berths = sum(1 for j in self.jetties if j.is_vessel_berth)
        barge_berths = sum(1 for j in self.jetties if j.is_barge_berth)
        
        if vessel_berths + barge_berths != len(self.jetties):
            raise ValueError("Some jetties have invalid types")
    
    @property
    def vessel_berths(self) -> List[Jetty]:
        """Get all vessel berths"""
        return [j for j in self.jetties if j.is_vessel_berth]
    
    @property
    def barge_berths(self) -> List[Jetty]:
        """Get all barge berths"""
        return [j for j in self.jetties if j.is_barge_berth]
    
    def get_jetty_by_id(self, jetty_id: str) -> Optional[Jetty]:
        """Get a jetty by ID"""
        for jetty in self.jetties:
            if jetty.id == jetty_id:
                return jetty
        return None
    
    def get_tank_by_id(self, tank_id: str) -> Optional[Tank]:
        """Get a tank by ID"""
        for tank in self.tanks:
            if tank.id == tank_id:
                return tank
        return None
    
    def get_pump_by_id(self, pump_id: str) -> Optional[Pump]:
        """Get a pump by ID"""
        for pump in self.pumps:
            if pump.id == pump_id:
                return pump
        return None
    
    def get_surveyor_by_id(self, surveyor_id: str) -> Optional[Surveyor]:
        """Get a surveyor by ID"""
        for surveyor in self.surveyors:
            if surveyor.id == surveyor_id:
                return surveyor
        return None
    
    def get_available_surveyors(self, timestamp: datetime) -> List[Surveyor]:
        """Get all surveyors available at a specific time"""
        return [s for s in self.surveyors if timestamp in s.availability and s.availability[timestamp]]
    
    def get_compatible_jetties_for_product(self, product: str) -> List[Jetty]:
        """Get all jetties that can handle a specific product"""
        return [j for j in self.jetties if j.can_handle_product(product) and j.is_operational]
    
    def find_compatible_jetties(self, vessel_length: float, vessel_draft: float, 
                               vessel_deadweight: float, product: str, 
                               vessel_type: str = "seagoing", connection_size: str = None) -> List[Jetty]:
        """
        Find jetties compatible with vessel and cargo requirements
        Implements business rules VC-1 through VC-5, PC-1 through PC-3, VT-1 through VT-2
        """
        compatible_jetties = []
        
        for jetty in self.jetties:
            if not jetty.is_operational:
                continue
            
            # Check vessel compatibility (VC-1 through VC-5, VT-1)
            vessel_compatible, reason = jetty.can_handle_vessel(
                vessel_length, vessel_draft, vessel_deadweight, vessel_type
            )
            if not vessel_compatible:
                continue
            
            # Check product compatibility (PC-1)
            if not jetty.can_handle_product(product):
                continue
            
            # Check connection compatibility (PC-2) - if specified
            if connection_size:
                connection_compatible, _ = jetty.check_connection_compatibility(connection_size)
                if not connection_compatible:
                    continue
            
            # Check vapor return requirement (PC-3)
            requires_vapor_return = product.lower() in ["benzene", "propylene_oxide", "acrylonitrile"]
            if requires_vapor_return:
                has_vapor_return = any(arm.has_vapor_return for arm in jetty.loading_arms if arm.is_operational)
                if not has_vapor_return:
                    continue
            
            compatible_jetties.append(jetty)
        
        return compatible_jetties
    
    def calculate_operational_complexity(self, vessel_size_category: str, product_type: str, 
                                       is_hazardous: bool, is_first_visit: bool, 
                                       multiple_products: bool) -> int:
        """
        Calculate operational complexity score as suggested in Section 7.1
        
        Args:
            vessel_size_category: 'small', 'medium', 'large', 'vlarge'
            product_type: Product being handled
            is_hazardous: Whether product is hazardous
            is_first_visit: Whether this is first visit for vessel
            multiple_products: Whether multiple products involved
            
        Returns:
            Complexity score (higher = more complex)
        """
        score = 0
        
        # Vessel size impact
        size_scores = {'small': 1, 'medium': 2, 'large': 3, 'vlarge': 4}
        score += size_scores.get(vessel_size_category, 2)
        
        # Product hazard level
        if is_hazardous:
            score += 2
        
        # First time vessel
        if is_first_visit:
            score += 2
        
        # Multiple products
        if multiple_products:
            score += 1
        
        # Specific product requirements
        if product_type.lower() in ["propylene_oxide", "acrylonitrile", "butane"]:
            score += 2  # Highly hazardous
        
        if product_type.lower() in ["benzene"]:
            score += 1  # Hazardous
        
        return score
    
    def validate_nomination(self, vessel_length: float, vessel_draft: float, vessel_deadweight: float,
                          product: str, vessel_type: str = "seagoing", connection_size: str = None,
                          wind_speed: float = 0, thunderstorm_active: bool = False) -> dict:
        """
        Validate a vessel nomination against all business rules
        
        Returns:
            Dict with 'valid' (bool), 'compatible_jetties' (list), and 'issues' (list)
        """
        issues = []
        compatible_jetties = self.find_compatible_jetties(
            vessel_length, vessel_draft, vessel_deadweight, product, vessel_type, connection_size
        )
        
        # Check for impossible nominations (Rule VC-1 through VC-5 violated)
        if not compatible_jetties:
            issues.append("No jetties can handle this vessel/product combination")
        
        # Check weather constraints
        weather_issues = []
        for jetty in compatible_jetties:
            can_operate, weather_reason = jetty.check_weather_constraints(wind_speed, thunderstorm_active)
            if not can_operate:
                weather_issues.append(f"Jetty {jetty.name}: {weather_reason}")
        
        if weather_issues:
            issues.extend(weather_issues)
            # Remove jetties that can't operate in current weather
            compatible_jetties = [
                j for j in compatible_jetties 
                if j.check_weather_constraints(wind_speed, thunderstorm_active)[0]
            ]
        
        return {
            'valid': len(compatible_jetties) > 0,
            'compatible_jetties': compatible_jetties,
            'issues': issues,
            'recommendations': self._generate_recommendations(compatible_jetties, product)
        }
    
    def _generate_recommendations(self, compatible_jetties: List[Jetty], product: str) -> List[str]:
        """Generate recommendations based on compatible jetties and product"""
        if not compatible_jetties:
            return ["Consider alternative products or vessel specifications"]
        
        recommendations = []
        
        # Recommend best jetty based on criteria from Section 9
        if product.lower() in ["propylene_oxide", "acrylonitrile"]:
            recommendations.append("Jetties 1 or 2 recommended for hazardous products")
        
        if product.lower() == "butane":
            recommendations.append("Jetty 6 recommended for butane operations")
        
        if product.lower() == "benzene":
            recommendations.append("Jetties 1 or 3 recommended for benzene")
        
        # Recommend based on flow rate capacity
        if compatible_jetties:
            best_jetty = max(compatible_jetties, key=lambda j: j.max_flow_rate)
            if best_jetty.max_flow_rate > 0:
                recommendations.append(f"Jetty {best_jetty.name} offers highest flow rate ({best_jetty.max_flow_rate} m³/h)")
        
        return recommendations
    
    def to_dict(self) -> dict:
        """Convert terminal to dictionary for serialization"""
        return {
            "name": self.name,
            "jetties": [jetty.to_dict() for jetty in self.jetties],
            "tanks": [tank.to_dict() for tank in self.tanks],
            "pumps": [pump.to_dict() for pump in self.pumps],
            "surveyors": [surveyor.to_dict() for surveyor in self.surveyors],
            "location": list(self.location) if self.location else None
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Terminal':
        """Create terminal from dictionary"""
        terminal = cls(
            name=data["name"],
            jetties=[Jetty.from_dict(j) for j in data["jetties"]],
            tanks=[Tank.from_dict(t) for t in data["tanks"]],
            pumps=[Pump.from_dict(p) for p in data["pumps"]],
            surveyors=[Surveyor.from_dict(s) for s in data["surveyors"]],
            location=tuple(data["location"]) if data["location"] else None
        )
        terminal._validate_terminal()
        return terminal
