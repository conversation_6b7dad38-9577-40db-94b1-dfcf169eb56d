from typing import List, Dict, Any, Optional

from ..database import Database
from ..db.models import VesselAISData


class AISIntegrationService:
    def __init__(self, db: Optional[Database] = None):
        self.db = db or Database()

    def process_ais_updates(self, ais_data_batch: List[Dict[str, Any]]) -> int:
        # Placeholder: persist raw rows and link when possible
        count = 0
        with self.db.get_session() as db:
            for row in ais_data_batch:
                rec = VesselAISData(
                    vessel_id=row.get('vessel_id'),
                    mmsi=row['mmsi'],
                    latitude=row.get('latitude'),
                    longitude=row.get('longitude'),
                    course=row.get('course'),
                    speed=row.get('speed'),
                    heading=row.get('heading'),
                    navigation_status=row.get('navigation_status'),
                    rate_of_turn=row.get('rate_of_turn'),
                    position_accuracy=row.get('position_accuracy'),
                    draught=row.get('draught'),
                    destination=row.get('destination'),
                    eta_raw=row.get('eta_raw'),
                    eta_parsed=row.get('eta_parsed'),
                    timestamp=row['timestamp'],
                    age_seconds=row.get('age_seconds'),
                    signal_quality=row.get('signal_quality'),
                    source=row.get('source')
                )
                db.add(rec)
                count += 1
            db.commit()
        return count

    def link_ais_to_vessel(self, mmsi: str, vessel_id: int) -> int:
        with self.db.get_session() as db:
            q = db.query(VesselAISData).filter_by(mmsi=mmsi, vessel_id=None)
            updated = q.update({VesselAISData.vessel_id: vessel_id})
            db.commit()
            return updated

    def update_vessel_position(self, vessel_id: int, position_data: Dict[str, Any]) -> bool:
        # Simplified: just insert a new record with the latest timestamp
        position_data = dict(position_data)
        position_data['vessel_id'] = vessel_id
        return self.process_ais_updates([position_data]) == 1

    def detect_arrival_departure(self, vessel_id: int, terminal_geofence) -> Optional[str]:
        # Placeholder: return status string
        return None


