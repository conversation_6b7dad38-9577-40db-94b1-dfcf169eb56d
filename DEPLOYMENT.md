# Jetty Planning Optimizer - Production Deployment Guide

This guide provides detailed instructions for deploying the Jetty Planning Optimizer in a production environment.

## Prerequisites

- Linux server (Ubuntu 20.04+ or similar)
- Docker and Docker Compose installed
- Git
- Internet connectivity (for pulling Docker images and accessing external APIs)
- API keys for:
  - AIS Stream API
  - Weather API (OpenWeatherMap)
  - Claude API (from Anthropic)

## System Requirements

- **CPU**: 2+ cores recommended
- **RAM**: 4GB minimum, 8GB recommended for larger terminals
- **Disk**: 10GB minimum for application and logs
- **Network**: Outbound access for API calls, inbound access to exposed ports

## Deployment Options

### Option 1: Docker Deployment (Recommended)

1. **Prepare the server**

   Update the system:
   ```bash
   sudo apt update
   sudo apt upgrade -y
   ```

   Install Docker and Docker Compose:
   ```bash
   sudo apt install -y docker.io docker-compose
   sudo systemctl enable --now docker
   sudo usermod -aG docker $USER
   ```
   Log out and log back in to apply group changes.

2. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/jetty-planning.git
   cd jetty-planning
   ```

3. **Configure environment variables**

   Create and edit the environment file:
   ```bash
   cp .env.example .env
   nano .env
   ```

   Configure the following required variables:
   ```
   VESSELFINDER_API_KEY=your_key_here
   WEATHER_API_KEY=your_key_here
   ANTHROPIC_API_KEY=your_key_here
   PRODUCTION=true
   API_HOST=0.0.0.0
   API_PORT=7000
   ```

4. **Deploy with Docker Compose**

   ```bash
   docker-compose up -d
   ```

   This will:
   - Build the Docker image
   - Start the container in detached mode
   - Map port 7000 to the host
   - Mount a volume for persistent data
   - Configure automatic restarts

## PostgreSQL Database Configuration

The application supports both SQLite (default) and PostgreSQL databases. For production deployments, PostgreSQL is recommended for better performance, concurrent access, and data integrity.

### PostgreSQL Migration Overview

The application includes a complete PostgreSQL migration system with:
- SQLAlchemy 2.x ORM models
- Alembic for schema migrations
- Automatic compatibility layer for existing code
- Data migration tools from SQLite to PostgreSQL

### Environment Configuration

Configure PostgreSQL connection in your `.env` file:

```bash
# Database backend selection
DB_BACKEND=postgres

# PostgreSQL connection details
DB_HOST=localhost
DB_PORT=4432
DB_NAME=planner
DB_USER=postgres
DB_PASSWORD=your_secure_password

# Connection URL (automatically constructed or set manually)
DATABASE_URL=postgresql+psycopg://postgres:your_secure_password@localhost:4432/planner

# Connection pool settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Database debugging
DB_ECHO=false
```

### PostgreSQL Setup Options

#### Option 1: External PostgreSQL Instance (Recommended)

Use an existing PostgreSQL server (version 12+):

1. **Create database and user**:
   ```sql
   CREATE DATABASE planner;
   CREATE USER jettyplanner WITH ENCRYPTED PASSWORD 'your_secure_password';
   GRANT ALL PRIVILEGES ON DATABASE planner TO jettyplanner;
   ```

2. **Configure connection in .env**:
   ```bash
   DB_BACKEND=postgres
   DB_HOST=your_postgres_host
   DB_PORT=5432
   DB_NAME=planner
   DB_USER=jettyplanner
   DB_PASSWORD=your_secure_password
   DATABASE_URL=postgresql+psycopg://jettyplanner:your_secure_password@your_postgres_host:5432/planner
   ```

#### Option 2: Docker Compose PostgreSQL

The compose file includes `postgres-primary` and `postgres-replica` services for redundancy via streaming replication:

```bash
# Start with PostgreSQL services
docker-compose up -d postgres-primary postgres-replica

# Configure app to use PostgreSQL
export DB_BACKEND=postgres
export DATABASE_URL=postgresql+psycopg://app_user:app_password@postgres-primary:5432/jettyplanner

# Start application
docker-compose up -d jetty-planning-app
```

### Database Migrations

#### Initial Schema Setup

1. **Initialize Alembic** (already done):
   ```bash
   alembic init alembic
   ```

2. **Run initial migration**:
   ```bash
   # Create all tables in PostgreSQL
   alembic upgrade head
   ```

#### Migrating Data from SQLite to PostgreSQL

Use the provided migration script to transfer existing data:

```bash
# Dry run to check what will be migrated
python scripts/migrate_sqlite_to_postgres.py --dry-run

# Perform actual migration
DB_BACKEND=postgres python scripts/migrate_sqlite_to_postgres.py

# Check migration verification report
cat reports/migration_verification_*.txt
```

The migration script:
- Preserves all existing data and relationships
- Handles data type conversions (SQLite to PostgreSQL)
- Provides verification reports with record counts
- Supports batch processing for large datasets
- Creates audit trail of the migration

#### Schema Updates

For future schema changes:

```bash
# Generate new migration
alembic revision --autogenerate -m "Description of changes"

# Apply migration to database
alembic upgrade head

# View migration history
alembic history

# Rollback to previous version (if needed)
alembic downgrade -1
```

### Database Operations

#### Backup and Restore

**PostgreSQL Backup**:
```bash
# Full database backup
pg_dump -h localhost -p 4432 -U postgres -d planner > backup_$(date +%Y%m%d_%H%M%S).sql

# Backup with compression
pg_dump -h localhost -p 4432 -U postgres -d planner | gzip > backup_$(date +%Y%m%d_%H%M%S).sql.gz

# Backup specific tables
pg_dump -h localhost -p 4432 -U postgres -d planner -t assignments -t vessels > assignments_vessels_backup.sql
```

**PostgreSQL Restore**:
```bash
# Restore from backup
psql -h localhost -p 4432 -U postgres -d planner < backup_20250101_120000.sql

# Restore from compressed backup
gunzip -c backup_20250101_120000.sql.gz | psql -h localhost -p 4432 -U postgres -d planner
```

#### Health Checks and Monitoring

**Database Health Check**:
```bash
# Test connection
psql -h localhost -p 4432 -U postgres -d planner -c "SELECT version();"

# Check table sizes
psql -h localhost -p 4432 -U postgres -d planner -c "
SELECT 
    schemaname,
    tablename,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;"

# Check active connections
psql -h localhost -p 4432 -U postgres -d planner -c "
SELECT 
    datname,
    usename,
    application_name,
    state,
    count(*)
FROM pg_stat_activity 
WHERE datname = 'planner'
GROUP BY datname, usename, application_name, state;"
```

**Application Health Check**:
```bash
# Test database connectivity through application
curl http://localhost:7000/api/terminal

# Check database backend being used
curl http://localhost:7000/api/health
```

### Performance Optimization

#### Connection Pool Tuning

Adjust connection pool settings based on your workload:

```bash
# For high-concurrency applications
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=40
DB_POOL_TIMEOUT=60
DB_POOL_RECYCLE=1800

# For low-concurrency applications
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
```

#### Index Optimization

The migration includes optimized indexes for common queries:

- `assignments(terminal_id, start_time)` for schedule queries
- `vessels(terminal_id, name)` for vessel lookups
- `jetties(terminal_id, name)` for jetty lookups
- Foreign key indexes for referential integrity

#### Query Performance

Enable query logging for optimization:

```bash
DB_ECHO=true  # Log all SQL queries (development only)
```

### Troubleshooting PostgreSQL Issues

**Common Connection Issues**:

1. **Connection refused**:
   - Check PostgreSQL service is running
   - Verify host and port in DATABASE_URL
   - Check firewall settings

2. **Authentication failed**:
   - Verify username and password
   - Check `pg_hba.conf` authentication settings
   - Ensure user has proper permissions

3. **Database doesn't exist**:
   ```bash
   psql -h localhost -p 4432 -U postgres -c "CREATE DATABASE planner;"
   ```

**Migration Issues**:

1. **Alembic version conflicts**:
   ```bash
   # Reset alembic version table
   alembic stamp head
   ```

2. **Data migration failures**:
   ```bash
   # Check migration logs
   python scripts/migrate_sqlite_to_postgres.py --dry-run
   
   # Review verification report
   cat reports/migration_verification_*.txt
   ```

**Performance Issues**:

1. **Slow queries**:
   ```sql
   -- Enable query statistics
   SELECT * FROM pg_stat_statements ORDER BY total_exec_time DESC LIMIT 10;
   ```

2. **Connection pool exhaustion**:
   ```bash
   # Monitor active connections
   psql -c "SELECT count(*) FROM pg_stat_activity;"
   
   # Adjust pool settings
   DB_POOL_SIZE=15
   DB_MAX_OVERFLOW=25
   ```

### Backup Strategy

**Automated Backup Script**:

Create `/usr/local/bin/backup-jettyplanner.sh`:

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/jettyplanner"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=7

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup database
pg_dump -h localhost -p 4432 -U postgres -d planner | gzip > $BACKUP_DIR/planner_$DATE.sql.gz

# Remove old backups
find $BACKUP_DIR -name "planner_*.sql.gz" -type f -mtime +$RETENTION_DAYS -delete

echo "Backup completed: planner_$DATE.sql.gz"
```

**Daily Backup Cron Job**:
```bash
# Add to crontab
0 2 * * * /usr/local/bin/backup-jettyplanner.sh
```

### Disaster Recovery

**Recovery Procedure**:

1. **Restore from backup**:
   ```bash
   # Stop application
   docker-compose stop jetty-planning-app
   
   # Restore database
   gunzip -c /var/backups/jettyplanner/planner_20250101_020000.sql.gz | \
     psql -h localhost -p 4432 -U postgres -d planner
   
   # Start application
   docker-compose start jetty-planning-app
   ```

2. **Fallback to SQLite**:
   ```bash
   # Temporarily switch back to SQLite
   export DB_BACKEND=sqlite
   docker-compose restart jetty-planning-app
   ```

5. **Verify deployment**

   Check the container status:
   ```bash
   docker-compose ps
   ```

   Check the logs:
   ```bash
   docker-compose logs
   ```

### Option 2: Native Deployment

1. **Prepare the server**

   Update the system:
   ```bash
   sudo apt update
   sudo apt upgrade -y
   ```

   Install Python and dependencies:
   ```bash
   sudo apt install -y python3 python3-pip python3-venv
   ```

2. **Create a dedicated user**

   ```bash
   sudo useradd -m -s /bin/bash jettyplanner
   sudo su - jettyplanner
   ```

3. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/jetty-planning.git
   cd jetty-planning
   ```

4. **Set up Python environment**

   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

5. **Configure environment variables**

   ```bash
   cp .env.example .env
   nano .env
   ```

   Configure the required variables.

6. **Create a systemd service**

   Create a service file:
   ```bash
   sudo nano /etc/systemd/system/jetty-planning.service
   ```

   Add the following content:
   ```ini
   [Unit]
   Description=Jetty Planning Optimizer
   After=network.target

   [Service]
   User=jettyplanner
   Group=jettyplanner
   WorkingDirectory=/home/<USER>/jetty-planning
   ExecStart=/home/<USER>/jetty-planning/venv/bin/python run.py --production
   Restart=always
   RestartSec=10
   Environment="PRODUCTION=true"
   Environment="API_HOST=0.0.0.0"
   Environment="API_PORT=7000"
   # Add other environment variables here or use EnvironmentFile

   [Install]
   WantedBy=multi-user.target
   ```

7. **Start and enable the service**

   ```bash
   sudo systemctl daemon-reload
   sudo systemctl start jetty-planning
   sudo systemctl enable jetty-planning
   ```

8. **Check service status**

   ```bash
   sudo systemctl status jetty-planning
   ```

## Web Server Configuration

For production environments, it's recommended to use a reverse proxy like Nginx to handle HTTPS and other web server functions.

### Nginx Configuration

1. **Install Nginx**

   ```bash
   sudo apt install -y nginx
   ```

2. **Create Nginx configuration**

   ```bash
   sudo nano /etc/nginx/sites-available/jetty-planning.conf
   ```

   Add the following content:
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://localhost:7000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

3. **Enable the site**

   ```bash
   sudo ln -s /etc/nginx/sites-available/jetty-planning.conf /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl reload nginx
   ```

4. **Set up HTTPS with Let's Encrypt**

   ```bash
   sudo apt install -y certbot python3-certbot-nginx
   sudo certbot --nginx -d your-domain.com
   ```

## Monitoring and Maintenance

### Monitoring

1. **Check application logs**

   Docker:
   ```bash
   docker-compose logs -f
   ```

   Native:
   ```bash
   sudo journalctl -u jetty-planning -f
   ```

2. **Set up health checks**

   ```bash
   curl http://localhost:7000/api/terminal
   ```

   This should return information about the terminal if the system is healthy.

### Backup and Restore

1. **Back up data**

   Docker:
   ```bash
   docker-compose exec jetty-planning-app tar -cvzf /app/data/backup-$(date +%Y%m%d).tar.gz /app/data
   docker cp jetty-planning-app:/app/data/backup-$(date +%Y%m%d).tar.gz .
   ```

   Native:
   ```bash
   sudo -u jettyplanner tar -cvzf ~/backup-$(date +%Y%m%d).tar.gz /home/<USER>/jetty-planning/data
   ```

2. **Restore data**

   Docker:
   ```bash
   docker cp backup-20250101.tar.gz jetty-planning-app:/app/
   docker-compose exec jetty-planning-app tar -xvzf /app/backup-20250101.tar.gz -C /
   ```

   Native:
   ```bash
   sudo -u jettyplanner tar -xvzf ~/backup-20250101.tar.gz -C /home/<USER>/jetty-planning/
   ```

### Updating the Application

1. **Docker update**

   ```bash
   git pull
   docker-compose down
   docker-compose build
   docker-compose up -d
   ```

2. **Native update**

   ```bash
   sudo systemctl stop jetty-planning
   sudo -u jettyplanner bash -c "cd /home/<USER>/jetty-planning && git pull"
   sudo -u jettyplanner bash -c "cd /home/<USER>/jetty-planning && source venv/bin/activate && pip install -r requirements.txt"
   sudo systemctl start jetty-planning
   ```

## Scaling

To scale the application for larger terminals or higher throughput:

1. **Increase resources**

   - Allocate more CPU and memory to the Docker container or VM
   - Optimize database access patterns
   - Consider splitting the backend and frontend services

2. **Enable clustering**

   For multiple instances, implement a load balancer like HAProxy in front of multiple application instances.

## Security Considerations

1. **API Key Management**

   - Never store API keys in your code repository
   - Use environment variables or a secrets management solution
   - Rotate keys periodically

2. **Network Security**

   - Restrict access to the application using firewalls
   - Use HTTPS for all external access
   - Implement rate limiting to prevent abuse

3. **Authentication**

   - Add user authentication for the web interface
   - Implement JWT-based API authentication
   - Set up role-based access control for different user types

## Troubleshooting

### Common Issues

1. **Application doesn't start**

   Check logs:
   ```bash
   docker-compose logs
   # or
   sudo journalctl -u jetty-planning
   ```

   Verify environment variables are set correctly.

2. **API endpoints return errors**

   - Check API keys are valid
   - Verify network connectivity to external services
   - Check disk space and system resources

3. **Optimization takes too long**

   - Increase system resources
   - Reduce optimization time window
   - Adjust optimization parameters

## Support

For commercial support or customization services, contact <EMAIL>.

## Windows Deployment (PowerShell)

### Prerequisites
- Windows 10/11
- PowerShell
- Git
- One of the following:
  - Docker Desktop (recommended), or
  - Python 3.10+

### Option A: Docker Desktop (recommended)
```powershell
# Clone repository
cd C:\
git clone <your-private-repo-url> Jettyplanner
cd C:\Jettyplanner

# Optional: create .env in repo root for real API keys
# VESSELFINDER_API_KEY=your_key
# WEATHER_API_KEY=your_key
# ANTHROPIC_API_KEY=your_key
# API_HOST=0.0.0.0
# API_PORT=7000
# PRODUCTION=true

# Start containers
docker compose up -d --build

# Verify containers & logs
docker compose ps | cat
docker compose logs -n 100 | cat
```

- Default app port is 7000. Confirm the port is available before binding; if not, edit the `ports:` mapping in `docker-compose.yml` (e.g., `"9001:7000"`) and rerun the start command.
- By default, the app uses SQLite at `data\jetty_planner.db`. The compose file also provisions Postgres; to use it, set `DATABASE_URL` accordingly.

### Option B: Native (no Docker)
```powershell
# Clone repository
cd C:\
git clone <your-private-repo-url> Jettyplanner
cd C:\Jettyplanner

# Create virtual environment & install dependencies
py -3.10 -m venv .venv
Set-ExecutionPolicy -Scope CurrentUser RemoteSigned -Force
./.venv/Scripts/Activate.ps1
python -m pip install -U pip
pip install -r requirements.txt

# Quick start with test data (no real keys needed)
python .\run.py --production --api --host 127.0.0.1 --port 7000 --test

# Or use real keys via .env in repo root, then run without --test
# .env example:
# VESSELFINDER_API_KEY=your_key
# WEATHER_API_KEY=your_key
# ANTHROPIC_API_KEY=your_key
# API_HOST=127.0.0.1
# API_PORT=7000
# PRODUCTION=true
# Start server with real keys
# python .\run.py --production --api --host 127.0.0.1 --port 7000

# Health check (optional)
curl http://localhost:7000/api/terminal | cat
```

Open http://localhost:7000 when running. If 7000 is taken, choose another port and pass it via `--port` or adjust the compose mapping as above.

### Windows update procedure
```powershell
cd C:\Jettyplanner
git pull

# Docker
docker compose down
docker compose build
docker compose up -d

# Native
./.venv/Scripts/Activate.ps1
pip install -r requirements.txt
python .\run.py --production --api --host 127.0.0.1 --port 7000
```