[1mdiff --git a/src/templates/schedule.html b/src/templates/schedule.html[m
[1mindex f566ce5..2315190 100644[m
[1m--- a/src/templates/schedule.html[m
[1m+++ b/src/templates/schedule.html[m
[36m@@ -765,8 +765,8 @@[m
                                     <span id="display-priority">5.0</span>[m
                                 </div>[m
                                 <div class="parameter-display">[m
[31m-                                    <label>Weather Safety Weight:</label>[m
[31m-                                    <span id="display-weather">5.0</span>[m
[32m+[m[32m                                    <label>Utilization Weight:</label>[m
[32m+[m[32m                                    <span id="display-weather">2.0</span>[m
                                 </div>[m
                                 <div class="parameter-display">[m
                                     <label>Planning Horizon:</label>[m
[36m@@ -1428,49 +1428,80 @@[m
         return s;[m
     }[m
 [m
[31m-    // Optimization preset configurations[m
[31m-    const optimizationPresets = {[m
[32m+[m[32m    // Dynamic optimization preset configurations loaded from settings[m
[32m+[m[32m    let optimizationPresets = {[m
[32m+[m[32m        // Default fallback presets (will be replaced by settings API)[m
         throughput: {[m
             name: "🚀 Maximum Throughput",[m
             description: "Prioritizes maximizing the number of vessels processed within the planning horizon. Ideal for high-demand periods when throughput is critical.",[m
[31m-            details: { throughput: "High", cost: "Medium", safety: "Medium" },[m
[32m+[m[32m            details: { throughput: "High", cost: "Medium", infrastructure: "Medium" },[m
             weights: {[m
                 throughput: 15.0,[m
                 demurrage: 3.0,[m
                 priority: 5.0,[m
[31m-                weather: 5.0,[m
[32m+[m[32m                utilization: 2.0,[m
                 horizon: 7,[m
                 granularity: 1[m
             }[m
         },[m
         cost: {[m
[31m-            name: "💰 Cost Optimized",[m
[31m-            description: "Focuses on minimizing demurrage costs and operational expenses. Best for periods when cost control is the primary concern.",[m
[31m-            details: { throughput: "Medium", cost: "High", safety: "Low" },[m
[32m+[m[32m            name: "💰 Cost Efficiency",[m
[32m+[m[32m            description: "Focuses on minimizing demurrage costs and operational expenses. Best for cost-controlled operations.",[m
[32m+[m[32m            details: { throughput: "Medium", cost: "High", infrastructure: "Medium" },[m
             weights: {[m
                 throughput: 8.0,[m
                 demurrage: 18.0,[m
                 priority: 2.0,[m
[31m-                weather: 1.0,[m
[32m+[m[32m                utilization: 3.0,[m
                 horizon: 14,[m
                 granularity: 2[m
             }[m
         },[m
[31m-        safety: {[m
[31m-            name: "🛡️ Safety First",[m
[31m-            description: "Prioritizes weather safety and customer priority requirements. Recommended during adverse weather conditions or for high-value customers.",[m
[31m-            details: { throughput: "Low", cost: "Medium", safety: "High" },[m
[32m+[m[32m        infrastructure: {[m
[32m+[m[32m            name: "🏗️ Infrastructure Efficiency",[m
[32m+[m[32m            description: "Optimizes jetty utilization and minimizes infrastructure conflicts. Ideal for maximizing facility efficiency.",[m
[32m+[m[32m            details: { throughput: "Medium", cost: "Medium", infrastructure: "High" },[m
             weights: {[m
[31m-                throughput: 5.0,[m
[32m+[m[32m                throughput: 10.0,[m
                 demurrage: 8.0,[m
[31m-                priority: 15.0,[m
[31m-                weather: 12.0,[m
[32m+[m[32m                priority: 6.0,[m
[32m+[m[32m                utilization: 15.0,[m
                 horizon: 10,[m
                 granularity: 1[m
             }[m
[32m+[m[32m        },[m
[32m+[m[32m        balanced: {[m
[32m+[m[32m            name: "⚖️ Balanced",[m
[32m+[m[32m            description: "Balanced approach across all optimization factors. Good general-purpose optimization strategy.",[m
[32m+[m[32m            details: { throughput: "Medium", cost: "Medium", infrastructure: "Medium" },[m
[32m+[m[32m            weights: {[m
[32m+[m[32m                throughput: 12.0,[m
[32m+[m[32m                demurrage: 10.0,[m
[32m+[m[32m                priority: 8.0,[m
[32m+[m[32m                utilization: 8.0,[m
[32m+[m[32m                horizon: 7,[m
[32m+[m[32m                granularity: 1[m
[32m+[m[32m            }[m
         }[m
     };[m
 [m
[32m+[m[32m    // Load optimization presets from settings API[m
[32m+[m[32m    async function loadOptimizationPresets() {[m
[32m+[m[32m        try {[m
[32m+[m[32m            const response = await fetch('/api/settings/optimization-presets');[m
[32m+[m[32m            if (response.ok) {[m
[32m+[m[32m                const settingsPresets = await response.json();[m
[32m+[m[32m                // Merge with defaults, prioritizing settings[m
[32m+[m[32m                optimizationPresets = { ...optimizationPresets, ...settingsPresets };[m
[32m+[m[32m                console.log('Loaded optimization presets from settings:', optimizationPresets);[m
[32m+[m[32m            } else {[m
[32m+[m[32m                console.warn('Failed to load optimization presets from settings, using defaults');[m
[32m+[m[32m            }[m
[32m+[m[32m        } catch (error) {[m
[32m+[m[32m            console.warn('Error loading optimization presets:', error);[m
[32m+[m[32m        }[m
[32m+[m[32m    }[m
[32m+[m
     // Current optimization parameters (will be updated by preset selection)[m
     let currentOptimizationParams = { ...optimizationPresets.throughput.weights };[m
 [m
[36m@@ -1509,6 +1540,11 @@[m
     // Update preset description and parameter displays[m
     function updatePresetDisplay(presetKey) {[m
         const preset = optimizationPresets[presetKey];[m
[32m+[m[32m        if (!preset) {[m
[32m+[m[32m            console.error('Preset not found:', presetKey);[m
[32m+[m[32m            return;[m
[32m+[m[32m        }[m
[32m+[m[41m        [m
         const descriptionDiv = document.getElementById('preset-description');[m
 [m
         descriptionDiv.innerHTML = `[m
[36m@@ -1518,7 +1554,7 @@[m
                 <div class="preset-details">[m
                     <span class="detail-item">Throughput: <strong>${preset.details.throughput}</strong></span>[m
                     <span class="detail-item">Cost Control: <strong>${preset.details.cost}</strong></span>[m
[31m-                    <span class="detail-item">Safety: <strong>${preset.details.safety}</strong></span>[m
[32m+[m[32m                    <span class="detail-item">Infrastructure: <strong>${preset.details.infrastructure}</strong></span>[m
                 </div>[m
             </div>[m
         `;[m
[36m@@ -1527,7 +1563,7 @@[m
         document.getElementById('display-throughput').textContent = preset.weights.throughput.toFixed(1);[m
         document.getElementById('display-demurrage').textContent = preset.weights.demurrage.toFixed(1);[m
         document.getElementById('display-priority').textContent = preset.weights.priority.toFixed(1);[m
[31m-        document.getElementById('display-weather').textContent = preset.weights.weather.toFixed(1);[m
[32m+[m[32m        document.getElementById('display-weather').textContent = (preset.weights.utilization || 0).toFixed(1);[m
         document.getElementById('display-horizon').textContent = preset.weights.horizon + ' Days';[m
         document.getElementById('display-granularity').textContent = preset.weights.granularity + ' Hour' + (preset.weights.granularity > 1 ? 's' : '');[m
 [m
