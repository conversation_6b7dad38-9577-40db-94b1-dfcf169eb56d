"""
Vessel Model

This module defines the Vessel and Barge classes for the petrochemical terminal.
"""

import logging
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Set, Tuple, Any
from datetime import datetime, timedelta, timezone
from enum import Enum
from ..utils.status_utils import normalize_status, is_valid_vessel_status, is_valid_vessel_transition


def _parse_datetime_safe(datetime_str: Optional[str]) -> Optional[datetime]:
    """
    Safely parse datetime string, ensuring timezone-aware result.
    If the string has no timezone info, assumes UTC.
    """
    if not datetime_str:
        return None
    
    try:
        dt = datetime.fromisoformat(datetime_str)
        # If timezone-naive, make it UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        return dt
    except ValueError:
        # If parsing fails, return None
        return None


class VesselType(Enum):
    """Type of vessel"""
    TANKER = "tanker"
    BARGE = "barge"


@dataclass
class Cargo:
    """Cargo to be loaded or unloaded"""
    product: str
    volume: float  # in cubic meters
    is_loading: bool  # True for loading, False for unloading
    tanks: List[str] = field(default_factory=list)  # Tank IDs for this cargo
    surveyor_required: bool = True
    completed_volume: float = 0.0  # Volume already loaded/unloaded

    def to_dict(self) -> dict:
        """Convert cargo to dictionary for serialization"""
        return {
            "product": self.product,
            "volume": self.volume,
            "is_loading": self.is_loading,
            "tanks": self.tanks,
            "surveyor_required": self.surveyor_required,
            "completed_volume": self.completed_volume
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'Cargo':
        """Create cargo from dictionary"""
        return cls(
            product=data["product"],
            volume=data["volume"],
            is_loading=data["is_loading"],
            tanks=data.get("tanks", []),
            surveyor_required=data.get("surveyor_required", True),
            completed_volume=data.get("completed_volume", 0.0)
        )


@dataclass
class VesselBase:
    """Base class for vessels and barges"""
    id: str
    name: str
    vessel_type: VesselType
    length: float  # in meters
    beam: float  # in meters
    draft: float  # in meters
    deadweight: float  # in tonnes
    cargoes: List[Cargo] = field(default_factory=list)
    status: str = "EN_ROUTE"  # Using string status instead of enum

    # Standardized ETA fields
    eta: Optional[datetime] = None  # User-specified/customer-provided ETA
    etd: Optional[datetime] = None  # User-specified/customer-provided ETD
    calculated_eta: Optional[datetime] = None  # System-calculated ETA based on AIS/conditions
    eta_confidence: int = 50  # Confidence score for ETA prediction (0-100)
    eta_source: str = "user"  # Source of ETA ("user", "ais_calculated", "ml_predicted")

    # Actual times
    actual_arrival: Optional[datetime] = None  # Actual arrival time (renamed from arrival_time)
    actual_departure: Optional[datetime] = None  # Actual departure time (renamed from departure_time)

    # Backward compatibility properties (deprecated - will be removed in future)
    @property
    def arrival_time(self) -> Optional[datetime]:
        """Deprecated: Use actual_arrival instead"""
        return self.actual_arrival

    @arrival_time.setter
    def arrival_time(self, value: Optional[datetime]):
        """Deprecated: Use actual_arrival instead"""
        self.actual_arrival = value

    @property
    def departure_time(self) -> Optional[datetime]:
        """Deprecated: Use actual_departure instead"""
        return self.actual_departure

    @departure_time.setter
    def departure_time(self, value: Optional[datetime]):
        """Deprecated: Use actual_departure instead"""
        self.actual_departure = value
    current_jetty: Optional[str] = None  # ID of currently assigned jetty
    customer: Optional[str] = None  # Customer name
    priority: int = 0  # Priority level (higher means more priority)
    capacity: float = 0.0  # Capacity in cubic meters
    width: float = 0.0  # Width/beam in meters
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Default setup/cleanup times (can be overridden by subclasses or instances)
    setup_duration: timedelta = field(default=timedelta(hours=12))  # Adjusted to achieve 10 hour average pre-pump time
    cleanup_duration: timedelta = field(default=timedelta(hours=3.6))  # Final adjustment to achieve 3 hour average post-pump time
    
    def __post_init__(self):
        """Validate and normalize vessel status after initialization"""
        if self.status:
            normalized_status = normalize_status(self.status)
            if not is_valid_vessel_status(normalized_status):
                raise ValueError(f"Invalid vessel status: {self.status}")
            self.status = normalized_status
    
    def total_cargo_volume(self) -> float:
        """Calculate total cargo volume"""
        return sum(cargo.volume for cargo in self.cargoes)
    
    def get_cargo_products(self) -> Set[str]:
        """Get unique products in cargoes"""
        return {cargo.product for cargo in self.cargoes}
    
    def get_loading_cargoes(self) -> List[Cargo]:
        """Get cargoes to be loaded"""
        return [cargo for cargo in self.cargoes if cargo.is_loading]
    
    def get_unloading_cargoes(self) -> List[Cargo]:
        """Get cargoes to be unloaded"""
        return [cargo for cargo in self.cargoes if not cargo.is_loading]
    
    def is_compatible_with_jetty(self, jetty_max_length: float, jetty_max_draft: float, 
                                jetty_max_deadweight: float) -> bool:
        """Check if vessel is compatible with jetty dimensions"""
        return (self.length <= jetty_max_length and 
                self.draft <= jetty_max_draft and 
                self.deadweight <= jetty_max_deadweight)
    
    def estimated_operation_time(self, max_flow_rate: float) -> timedelta:
        """Estimate operation time based on cargo volume and flow rate"""
        if max_flow_rate <= 0:
            return timedelta(hours=24)  # Default to 24 hours if flow rate is unknown
        
        total_volume = self.total_cargo_volume()
        hours = total_volume / max_flow_rate
        
        # Ensure division by zero is handled if total_volume is 0 or max_flow_rate is <= 0
        if total_volume <= 0:
            operation_hours = 0.0
        elif max_flow_rate <= 0:
            logging.warning(f"Max flow rate is zero or negative for vessel {self.id}. Cannot estimate operation time accurately. Returning base setup/cleanup time.")
            # Return only setup + cleanup, or maybe raise an error? For now, just return base times.
            return self.setup_duration + self.cleanup_duration 
        else:
            operation_hours = total_volume / max_flow_rate
            
        # Use the instance attributes for setup/cleanup
        total_duration = timedelta(hours=operation_hours) + self.setup_duration + self.cleanup_duration
            
        return total_duration
    
    def update_status(self, new_status: str) -> Tuple[bool, str]:
        """
        Update vessel status with validation
        
        Args:
            new_status: The new status string
            
        Returns:
            Tuple of (success, message)
        """
        normalized_new_status = normalize_status(new_status)
        is_valid, message = is_valid_vessel_transition(self.status, normalized_new_status)
        
        if is_valid:
            self.status = normalized_new_status
            return True, ""
        
        return False, message

    def to_dict(self) -> dict:
        """Convert vessel to dictionary for serialization"""
        return {
            "id": self.id,
            "name": self.name,
            "vessel_type": self.vessel_type.value,
            "length": self.length,
            "beam": self.beam,
            "draft": self.draft,
            "deadweight": self.deadweight,
            "cargoes": [cargo.to_dict() for cargo in self.cargoes],
            "status": self.status,  # Now a string, not enum
            "eta": self.eta.isoformat() if self.eta else None,
            "etd": self.etd.isoformat() if self.etd else None,
            "arrival_time": self.arrival_time.isoformat() if self.arrival_time else None,
            "departure_time": self.departure_time.isoformat() if self.departure_time else None,
            "current_jetty": self.current_jetty,
            "customer": self.customer,
            "priority": self.priority,
            "setup_duration": str(self.setup_duration),
            "cleanup_duration": str(self.cleanup_duration),
            "capacity": self.capacity,
            "width": self.width,
            "metadata": self.metadata
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'VesselBase':
        """Create vessel from dictionary"""
        return cls(
            id=data["id"],
            name=data["name"],
            vessel_type=VesselType(data["vessel_type"]),
            length=data["length"],
            beam=data["beam"],
            draft=data["draft"],
            deadweight=data["deadweight"],
            cargoes=[Cargo.from_dict(c) for c in data["cargoes"]],
            status=data["status"],  # Now a string, not enum

            # Standardized ETA fields
            eta=_parse_datetime_safe(data.get("eta")),
            etd=_parse_datetime_safe(data.get("etd")),
            calculated_eta=_parse_datetime_safe(data.get("calculated_eta")),
            eta_confidence=data.get("eta_confidence", 50),
            eta_source=data.get("eta_source", "user"),

            # Actual times (with backward compatibility)
            actual_arrival=_parse_datetime_safe(data.get("actual_arrival") or data.get("arrival_time")),
            actual_departure=_parse_datetime_safe(data.get("actual_departure") or data.get("departure_time")),
            current_jetty=data.get("current_jetty"),
            customer=data.get("customer"),
            priority=data.get("priority", 0),
            capacity=data.get("capacity", 0.0),  # Use get method with default value of 0.0
            width=data.get("width", 0.0),  # Also handle missing width field
            metadata=data.get("metadata", {})
        )


@dataclass
class Vessel(VesselBase):
    """Large vessel (tanker) for loading/unloading at vessel berths"""
    cargo_type: Optional[str] = None
    flag: Optional[str] = None
    imo: Optional[str] = None
    mmsi: Optional[str] = None
    customs_cleared: bool = False
    last_port: Optional[str] = None
    next_port: Optional[str] = None
    
    def __post_init__(self):
        super().__post_init__()  # Make sure parent's __post_init__ runs for status validation
        if self.vessel_type != VesselType.TANKER:
            self.vessel_type = VesselType.TANKER


@dataclass
class Barge(VesselBase):
    """Smaller barge for loading/unloading at barge berths"""
    owner: Optional[str] = None
    registration_number: Optional[str] = None
    tug_boat: Optional[str] = None  # Name of tug boat if applicable
    operation_type: Optional[str] = None  # Loading or unloading
    has_crane: bool = False
    
    def __post_init__(self):
        super().__post_init__()  # Make sure parent's __post_init__ runs for status validation
        if self.vessel_type != VesselType.BARGE:
            self.vessel_type = VesselType.BARGE
        # Override durations for barges (shorter times)
        self.setup_duration = timedelta(hours=4.0)  # Further adjusted to maintain proportion with vessels
        self.cleanup_duration = timedelta(hours=0.9)  # Further adjusted to maintain proportion with vessels
