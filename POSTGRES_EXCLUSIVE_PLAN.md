# PostgreSQL Exclusive Database Plan

## Overview
This plan outlines the steps to transition <PERSON>y<PERSON><PERSON>ner to work exclusively with PostgreSQL, removing SQLite support and simplifying the codebase.

## Current State
- **Dual Database Support**: Application supports both PostgreSQL and SQLite
- **Production**: Already using PostgreSQL (DB_BACKEND=postgres)
- **Architecture**: Unified interface with backend detection

## Migration Plan

### Phase 1: Code Cleanup
1. **Remove SQLite Implementation**
   - Delete `src/database.py` (1,403 lines of SQLite code)
   - Remove SQLite-specific imports and dependencies

2. **Simplify Database Interface**
   - Rename `src/persistence/adapter.py` to `src/database.py`
   - Remove dual-backend detection logic
   - Update all imports across the application

3. **Clean Configuration**
   - Remove `DB_BACKEND` environment variable
   - Simplify `src/db/session.py` to PostgreSQL-only
   - Update `.env.example` to remove SQLite references

### Phase 2: Code Refactoring
1. **Direct PostgreSQL Integration**
   - Remove `DatabaseAdapter` wrapper class
   - Integrate PostgreSQL methods directly into `Database` class
   - Remove backend detection in `__init__()`

2. **Update Dependencies**
   - Remove `sqlite3` imports
   - Ensure all PostgreSQL dependencies are properly declared
   - Clean up unused SQLite-related packages

### Phase 3: Testing & Validation
1. **Verify Functionality**
   - Test all CRUD operations
   - Verify analytics features work correctly
   - Ensure vessel registry and advanced features function

2. **Update Documentation**
   - Update README.md to reflect PostgreSQL-only setup
   - Remove SQLite references from deployment docs
   - Update Docker configuration if needed

### Phase 4: Deployment Preparation
1. **Environment Configuration**
   - Ensure all environments use PostgreSQL
   - Remove fallback SQLite logic
   - Update Docker Compose for PostgreSQL-only setup

2. **Migration Scripts**
   - Keep existing Alembic migrations
   - Remove SQLite-to-PostgreSQL migration scripts
   - Ensure clean database initialization

## Benefits
- **Simplified Codebase**: Remove ~1,400 lines of duplicate SQLite code
- **Reduced Complexity**: Single database backend, easier maintenance
- **Full Feature Access**: Use all PostgreSQL-specific features (analytics, vessel registry, etc.)
- **Better Performance**: Optimized for PostgreSQL without abstraction overhead

## Files to Modify
### Remove
- `src/database.py` (SQLite implementation)
- `scripts/migrate_sqlite_to_postgres.py`
- SQLite database files in `data/`

### Update
- `src/persistence/adapter.py` → `src/database.py`
- `src/db/session.py` (remove SQLite logic)
- All files importing database classes
- `.env.example` configuration
- Documentation files

## Estimated Effort
- **Development**: 4-6 hours
- **Testing**: 2-3 hours
- **Documentation**: 1-2 hours

## Risks & Mitigation
- **Risk**: Breaking existing functionality
- **Mitigation**: Comprehensive testing before deployment
- **Risk**: Configuration issues
- **Mitigation**: Clear environment variable documentation

## Success Criteria
✅ Application runs exclusively on PostgreSQL  
✅ All features work without SQLite fallback  
✅ Codebase is simplified and maintainable  
✅ No performance degradation  
✅ Clean deployment process