from typing import Optional, Dict, Any
from datetime import datetime

from ..database import Database
from ..db.models import VesselVisit


class VisitTrackingService:
    def __init__(self, db: Optional[Database] = None):
        self.db = db or Database()

    def create_visit(self, vessel_id: int, terminal_id: str, **visit_data: Any) -> int:
        with self.db.get_session() as db:
            visit = VesselVisit(
                vessel_id=vessel_id,
                terminal_id=terminal_id,
                visit_number=visit_data.get('visit_number'),
                external_reference=visit_data.get('external_reference'),
                estimated_arrival=visit_data.get('estimated_arrival'),
                estimated_departure=visit_data.get('estimated_departure'),
                operation_type=visit_data.get('operation_type'),
                cargo_types=visit_data.get('cargo_types'),
                total_cargo_volume=visit_data.get('total_cargo_volume'),
                status=visit_data.get('status', 'PLANNED'),
                berth_assignments=visit_data.get('berth_assignments'),
                customer=visit_data.get('customer'),
                agent=visit_data.get('agent'),
                priority=visit_data.get('priority', 1),
                notes=visit_data.get('notes')
            )
            db.add(visit)
            db.commit()
            db.refresh(visit)
            return visit.id

    def update_visit_status(self, visit_id: int, status: str) -> bool:
        with self.db.get_session() as db:
            visit = db.query(VesselVisit).filter_by(id=visit_id).first()
            if not visit:
                return False
            visit.status = status
            db.commit()
            return True

    def record_arrival_departure(self, visit_id: int, timestamp: datetime, event_type: str) -> bool:
        with self.db.get_session() as db:
            visit = db.query(VesselVisit).filter_by(id=visit_id).first()
            if not visit:
                return False
            if event_type == 'arrival_estimated':
                visit.estimated_arrival = timestamp
            elif event_type == 'arrival_actual':
                visit.actual_arrival = timestamp
            elif event_type == 'departure_estimated':
                visit.estimated_departure = timestamp
            elif event_type == 'departure_actual':
                visit.actual_departure = timestamp
            db.commit()
            return True

    def calculate_visit_metrics(self, visit_id: int) -> bool:
        with self.db.get_session() as db:
            visit = db.query(VesselVisit).filter_by(id=visit_id).first()
            if not visit:
                return False
            # Simple minute-based calculations
            if visit.actual_arrival and visit.estimated_arrival:
                visit.actual_berth_time = int((visit.actual_arrival - visit.estimated_arrival).total_seconds() / 60)
            if visit.actual_departure and visit.actual_arrival:
                visit.actual_operation_time = int((visit.actual_departure - visit.actual_arrival).total_seconds() / 60)
            if visit.actual_departure and visit.estimated_arrival:
                visit.total_port_time = int((visit.actual_departure - visit.estimated_arrival).total_seconds() / 60)
            db.commit()
            return True


