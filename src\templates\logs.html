{% extends "base.html" %}
{% block title %}Schedule Change Logs - Terneuzen Terminal Jetty Planning{% endblock %}
{% block head %}
    <style>
        .logs-container {
            padding: 1.5rem;
        }

        .logs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }

        .logs-title {
            color: #003b6f;
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
        }

        .logs-stats {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .stat-badge {
            background: #f8f9fa;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .stat-badge strong {
            color: #003b6f;
        }

        /* Filters Section */
        .filters-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid #dee2e6;
        }

        .filters-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .filters-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #003b6f;
            margin: 0;
        }

        .filters-toggle {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filters-toggle:hover {
            color: #003b6f;
        }

        .filters-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-weight: 500;
            color: #495057;
            font-size: 0.9rem;
        }

        .filter-input {
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .filter-input:focus {
            outline: none;
            border-color: #003b6f;
            box-shadow: 0 0 0 0.2rem rgba(0, 59, 111, 0.25);
        }

        .date-range-inputs {
            display: flex;
            gap: 0.5rem;
        }

        .date-range-inputs .filter-input {
            flex: 1;
        }

        .filter-actions {
            display: flex;
            gap: 0.5rem;
            align-items: end;
        }

        .btn-filter {
            background: #003b6f;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-filter:hover {
            background: #002a52;
        }

        .btn-clear {
            background: #6c757d;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .btn-clear:hover {
            background: #5a6268;
        }

        /* Logs Table */
        .logs-table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
            max-height: 70vh;
            overflow-y: auto;
        }

        .logs-table {
            width: 100%;
            border-collapse: collapse;
        }

        .logs-table th {
            background: #f8f9fa;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: #003b6f;
            border-bottom: 2px solid #dee2e6;
            font-size: 0.9rem;
        }

        .logs-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.9rem;
            vertical-align: top;
        }

        .logs-table tbody tr:hover {
            background: #f8f9fa;
        }

        .logs-table tbody tr:last-child td {
            border-bottom: none;
        }

        .log-time {
            font-weight: 500;
            color: #495057;
            white-space: nowrap;
        }

        .log-assignment {
            font-weight: 500;
            color: #003b6f;
        }

        .log-vessel {
            color: #495057;
        }

        .log-jetty {
            font-weight: 500;
            color: #28a745;
        }

        .log-change {
            max-width: 300px;
        }

        .log-old-value {
            color: #dc3545;
            text-decoration: line-through;
        }

        .log-new-value {
            color: #28a745;
            font-weight: 500;
        }

        .log-reason {
            max-width: 250px;
            line-height: 1.4;
        }

        .reason-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: capitalize;
        }

        .reason-operational {
            background: #e3f2fd;
            color: #1976d2;
        }

        .reason-vessel {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .reason-commercial {
            background: #e8f5e8;
            color: #388e3c;
        }

        .reason-terminal {
            background: #fff3e0;
            color: #f57c00;
        }

        .reason-regulatory {
            background: #fce4ec;
            color: #c2185b;
        }

        .reason-other {
            background: #f5f5f5;
            color: #616161;
        }

        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }

        .pagination-info {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .pagination-controls {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .pagination-btn {
            padding: 0.5rem 0.75rem;
            border: 1px solid #dee2e6;
            background: white;
            color: #495057;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #003b6f;
            color: white;
            border-color: #003b6f;
        }

        .pagination-btn:disabled {
            background: #e9ecef;
            color: #6c757d;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: #003b6f;
            color: white;
            border-color: #003b6f;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-size-selector label {
            font-size: 0.9rem;
            color: #495057;
        }

        .page-size-selector select {
            padding: 0.25rem 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        /* Export Section */
        .export-section {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .export-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .export-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #003b6f;
            margin: 0;
        }

        .export-controls {
            display: flex;
            gap: 0.5rem;
        }

        .btn-export {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-export:hover {
            background: #218838;
        }

        .export-format-selector {
            display: flex;
            gap: 0.5rem;
        }

        .format-option {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .format-option input[type="radio"] {
            margin: 0;
        }

        /* Loading and Empty States */
        .loading-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .loading-spinner {
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .logs-container {
                padding: 1rem;
            }

            .logs-header {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }

            .logs-stats {
                flex-wrap: wrap;
            }

            .filters-content {
                grid-template-columns: 1fr;
            }

            .date-range-inputs {
                flex-direction: column;
            }

            .filter-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .pagination-container {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .export-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .logs-table {
                font-size: 0.8rem;
            }

            .logs-table th,
            .logs-table td {
                padding: 0.5rem;
            }

            .log-change {
                max-width: 200px;
            }

            .log-reason {
                max-width: 150px;
            }
        }

    </style>
{% endblock %}
{% block header %}Schedule Change Logs{% endblock %}
{% block user_actions %}
    <a href="/schedule" class="btn btn-secondary btn-sm">
        <i class="fas fa-arrow-left"></i> Back to Schedule
    </a>
    <button class="btn btn-info btn-sm" id="refresh-logs">
        <i class="fas fa-sync-alt"></i> Refresh
    </button>
{% endblock %}
{% block content %}
    <div class="logs-container">
        <!-- Header Section -->
        <div class="logs-header">
            <h2 class="logs-title">Schedule Change Logs</h2>
            <div class="logs-stats">
                <span class="stat-badge">Total: <strong id="total-logs">0</strong></span>
                <span class="stat-badge">Today: <strong id="today-logs">0</strong></span>
                <span class="stat-badge">This Week: <strong id="week-logs">0</strong></span>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <div class="filters-header">
                <h3 class="filters-title">Filters</h3>
                <button class="filters-toggle" id="toggle-filters">
                    <i class="fas fa-chevron-up"></i>
                    <span>Hide Filters</span>
                </button>
            </div>
            <div class="filters-content" id="filters-content">
                <div class="filter-group">
                    <label class="filter-label">Date Range</label>
                    <div class="date-range-inputs">
                        <input type="date" class="filter-input" id="start-date" placeholder="Start Date">
                        <input type="date" class="filter-input" id="end-date" placeholder="End Date">
                    </div>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Vessel Name</label>
                    <input type="text" class="filter-input" id="vessel-filter" placeholder="Filter by vessel name">
                </div>
                <div class="filter-group">
                    <label class="filter-label">Jetty</label>
                    <input type="text" class="filter-input" id="jetty-filter" placeholder="Filter by jetty">
                </div>
                <div class="filter-group">
                    <label class="filter-label">Reason Category</label>
                    <select class="filter-input" id="reason-category-filter">
                        <option value="">All Categories</option>
                        <option value="operational">Operational</option>
                        <option value="vessel">Vessel-Related</option>
                        <option value="commercial">Commercial</option>
                        <option value="terminal">Terminal Operations</option>
                        <option value="regulatory">Regulatory</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="filter-actions">
                    <button class="btn-filter" id="apply-filters">
                        <i class="fas fa-filter"></i>
                        Apply Filters
                    </button>
                    <button class="btn-clear" id="clear-filters">
                        <i class="fas fa-times"></i>
                        Clear
                    </button>
                </div>
            </div>
        </div>

        <!-- Export Section -->
        <div class="export-section">
            <div class="export-header">
                <h3 class="export-title">Export Logs</h3>
                <div class="export-controls">
                    <div class="export-format-selector">
                        <label class="format-option">
                            <input type="radio" name="export-format" value="csv" checked>
                            CSV
                        </label>
                        <label class="format-option">
                            <input type="radio" name="export-format" value="json">
                            JSON
                        </label>
                    </div>
                    <button class="btn-export" id="export-logs">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Logs Table -->
        <div class="logs-table-container">
            <table class="logs-table">
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Assignment</th>
                        <th>Vessel</th>
                        <th>Jetty</th>
                        <th>Change Details</th>
                        <th>Reason</th>
                    </tr>
                </thead>
                <tbody id="logs-table-body">
                    <tr>
                        <td colspan="6" class="loading-state">
                            <i class="fas fa-spinner loading-spinner"></i>
                            <div>Loading logs...</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info" id="pagination-info">
                Showing 0 to 0 of 0 entries
            </div>
            <div class="pagination-controls">
                <div class="page-size-selector">
                    <label for="page-size">Show:</label>
                    <select id="page-size" class="filter-input">
                        <option value="25">25</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                    </select>
                </div>
                <button class="pagination-btn" id="prev-page" disabled>
                    <i class="fas fa-chevron-left"></i> Previous
                </button>
                <span id="page-numbers"></span>
                <button class="pagination-btn" id="next-page" disabled>
                    Next <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
{% endblock %}
{% block scripts %}
    <script nonce="{{ nonce }}">
        class LogsManager {
            constructor() {
                this.currentPage = 1;
                this.pageSize = 50;
                this.totalLogs = 0;
                this.allLogs = [];
                this.filteredLogs = [];
                this.filters = {
                    startDate: '',
                    endDate: '',
                    vessel: '',
                    jetty: '',
                    reasonCategory: ''
                };

                this.init();
            }

            init() {
                this.bindEvents();
                this.loadLogs();
                this.setDefaultDateRange();
            }

            bindEvents() {
                // Filter events
                document.getElementById('apply-filters').addEventListener('click', () => this.applyFilters());
                document.getElementById('clear-filters').addEventListener('click', () => this.clearFilters());
                document.getElementById('toggle-filters').addEventListener('click', () => this.toggleFilters());

                // Pagination events
                document.getElementById('prev-page').addEventListener('click', () => this.changePage(this.currentPage - 1));
                document.getElementById('next-page').addEventListener('click', () => this.changePage(this.currentPage + 1));
                document.getElementById('page-size').addEventListener('change', (e) => {
                    this.pageSize = parseInt(e.target.value);
                    this.currentPage = 1;
                    this.renderLogs();
                });

                // Export events
                document.getElementById('export-logs').addEventListener('click', () => this.exportLogs());

                // Refresh event
                document.getElementById('refresh-logs').addEventListener('click', () => this.loadLogs());

                // Enter key support for filters
                document.querySelectorAll('.filter-input').forEach(input => {
                    input.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.applyFilters();
                        }
                    });
                });
            }

            setDefaultDateRange() {
                const endDate = new Date();
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - 30); // Last 30 days

                document.getElementById('start-date').value = this.formatDateForInput(startDate);
                document.getElementById('end-date').value = this.formatDateForInput(endDate);

                this.filters.startDate = this.formatDateForInput(startDate);
                this.filters.endDate = this.formatDateForInput(endDate);
            }

            formatDateForInput(date) {
                return date.toISOString().split('T')[0];
            }

            async loadLogs() {
                try {
                    this.showLoading();
                    const response = await fetch('/api/schedule/changes?limit=1000');
                    if (!response.ok) {
                        throw new Error('Failed to load logs');
                    }
                    const data = await response.json();
                    this.allLogs = data || [];
                    this.applyFilters();
                    this.updateStats();
                } catch (error) {
                    console.error('Error loading logs:', error);
                    this.showError('Failed to load logs. Please try again.');
                }
            }

            applyFilters() {
                // Update filter values
                this.filters.startDate = document.getElementById('start-date').value;
                this.filters.endDate = document.getElementById('end-date').value;
                this.filters.vessel = document.getElementById('vessel-filter').value.toLowerCase();
                this.filters.jetty = document.getElementById('jetty-filter').value.toLowerCase();
                this.filters.reasonCategory = document.getElementById('reason-category-filter').value;

                // Filter logs
                this.filteredLogs = this.allLogs.filter(log => {
                    // Date filter
                    if (this.filters.startDate && log.changed_at) {
                        const logDate = new Date(log.changed_at).toISOString().split('T')[0];
                        if (logDate < this.filters.startDate) return false;
                    }
                    if (this.filters.endDate && log.changed_at) {
                        const logDate = new Date(log.changed_at).toISOString().split('T')[0];
                        if (logDate > this.filters.endDate) return false;
                    }

                    // Vessel filter
                    if (this.filters.vessel && log.vessel_name) {
                        if (!log.vessel_name.toLowerCase().includes(this.filters.vessel)) return false;
                    }

                    // Jetty filter
                    if (this.filters.jetty && log.jetty_name) {
                        if (!log.jetty_name.toLowerCase().includes(this.filters.jetty)) return false;
                    }

                    // Reason category filter
                    if (this.filters.reasonCategory) {
                        const reasonText = (log.reason || '').toLowerCase();
                        const category = this.getReasonCategory(reasonText);
                        if (category !== this.filters.reasonCategory) return false;
                    }

                    return true;
                });

                this.totalLogs = this.filteredLogs.length;
                this.currentPage = 1;
                this.renderLogs();
            }

            clearFilters() {
                document.getElementById('start-date').value = '';
                document.getElementById('end-date').value = '';
                document.getElementById('vessel-filter').value = '';
                document.getElementById('jetty-filter').value = '';
                document.getElementById('reason-category-filter').value = '';

                this.filters = {
                    startDate: '',
                    endDate: '',
                    vessel: '',
                    jetty: '',
                    reasonCategory: ''
                };

                this.filteredLogs = [...this.allLogs];
                this.totalLogs = this.filteredLogs.length;
                this.currentPage = 1;
                this.renderLogs();
            }

            toggleFilters() {
                const content = document.getElementById('filters-content');
                const toggle = document.getElementById('toggle-filters');
                const icon = toggle.querySelector('i');
                const text = toggle.querySelector('span');

                if (content.style.display === 'none') {
                    content.style.display = 'grid';
                    icon.className = 'fas fa-chevron-up';
                    text.textContent = 'Hide Filters';
                } else {
                    content.style.display = 'none';
                    icon.className = 'fas fa-chevron-down';
                    text.textContent = 'Show Filters';
                }
            }

            renderLogs() {
                const tbody = document.getElementById('logs-table-body');
                const startIndex = (this.currentPage - 1) * this.pageSize;
                const endIndex = Math.min(startIndex + this.pageSize, this.totalLogs);
                const logsToShow = this.filteredLogs.slice(startIndex, endIndex);

                if (logsToShow.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="6" class="empty-state">
                                <i class="fas fa-inbox"></i>
                                <div>No logs found matching your filters.</div>
                            </td>
                        </tr>
                    `;
                } else {
                    tbody.innerHTML = logsToShow.map(log => this.renderLogRow(log)).join('');
                }

                this.updatePagination();
            }

            renderLogRow(log) {
                const time = this.formatDateTime(log.changed_at);
                const reasonText = log.reason || '';
                const reasonCategory = this.getReasonCategory(reasonText);
                const reasonBadgeClass = `reason-${reasonCategory}`;

                return `
                    <tr>
                        <td class="log-time">${time}</td>
                        <td class="log-assignment">#${log.assignment_id || ''}</td>
                        <td class="log-vessel">${log.vessel_name || ''}</td>
                        <td class="log-jetty">${log.jetty_name || ''}</td>
                        <td class="log-change">
                            ${this.renderChangeDetails(log)}
                        </td>
                        <td class="log-reason">
                            <span class="reason-badge ${reasonBadgeClass}">${reasonCategory}</span>
                            <div style="margin-top: 0.25rem;">${this.escapeHtml(reasonText)}</div>
                        </td>
                    </tr>
                `;
            }

            renderChangeDetails(log) {
                const changes = [];

                if (log.old_start_time && log.new_start_time && log.old_start_time !== log.new_start_time) {
                    changes.push(`
                        <div><strong>Start Time:</strong></div>
                        <div class="log-old-value">${this.formatDateTime(log.old_start_time)}</div>
                        <div class="log-new-value">${this.formatDateTime(log.new_start_time)}</div>
                    `);
                }

                if (log.old_end_time && log.new_end_time && log.old_end_time !== log.new_end_time) {
                    changes.push(`
                        <div><strong>End Time:</strong></div>
                        <div class="log-old-value">${this.formatDateTime(log.old_end_time)}</div>
                        <div class="log-new-value">${this.formatDateTime(log.new_end_time)}</div>
                    `);
                }

                if (log.old_jetty_name && log.new_jetty_name && log.old_jetty_name !== log.new_jetty_name) {
                    changes.push(`
                        <div><strong>Jetty:</strong></div>
                        <div class="log-old-value">${log.old_jetty_name}</div>
                        <div class="log-new-value">${log.new_jetty_name}</div>
                    `);
                }

                return changes.length > 0 ? changes.join('<hr style="margin: 0.5rem 0; border: none; border-top: 1px solid #dee2e6;">') : '<em>No change details available</em>';
            }

            getReasonCategory(reasonText) {
                const text = reasonText.toLowerCase();
                if (text.includes('weather') || text.includes('port congestion') || text.includes('berthing') || text.includes('tide') || text.includes('pilot')) {
                    return 'operational';
                } else if (text.includes('vessel') || text.includes('crew') || text.includes('cargo preparation') || text.includes('documentation') || text.includes('eta change')) {
                    return 'vessel';
                } else if (text.includes('customer') || text.includes('priority') || text.includes('cargo specification') || text.includes('commercial') || text.includes('negotiation')) {
                    return 'commercial';
                } else if (text.includes('equipment') || text.includes('tank') || text.includes('pipeline') || text.includes('safety') || text.includes('resource')) {
                    return 'terminal';
                } else if (text.includes('port authority') || text.includes('environmental') || text.includes('safety inspection') || text.includes('customs')) {
                    return 'regulatory';
                } else {
                    return 'other';
                }
            }

            updatePagination() {
                const totalPages = Math.ceil(this.totalLogs / this.pageSize);
                const startIndex = (this.currentPage - 1) * this.pageSize + 1;
                const endIndex = Math.min(this.currentPage * this.pageSize, this.totalLogs);

                // Update pagination info
                document.getElementById('pagination-info').textContent =
                    `Showing ${startIndex} to ${endIndex} of ${this.totalLogs} entries`;

                // Update page buttons
                document.getElementById('prev-page').disabled = this.currentPage === 1;
                document.getElementById('next-page').disabled = this.currentPage === totalPages;

                // Update page numbers
                this.renderPageNumbers(totalPages);
            }

            renderPageNumbers(totalPages) {
                const container = document.getElementById('page-numbers');
                const pages = [];

                if (totalPages <= 7) {
                    for (let i = 1; i <= totalPages; i++) {
                        pages.push(this.createPageButton(i, i === this.currentPage));
                    }
                } else {
                    // Always show first page
                    pages.push(this.createPageButton(1, this.currentPage === 1));

                    if (this.currentPage > 4) {
                        pages.push('<span>...</span>');
                    }

                    // Show pages around current page
                    const start = Math.max(2, this.currentPage - 1);
                    const end = Math.min(totalPages - 1, this.currentPage + 1);

                    for (let i = start; i <= end; i++) {
                        pages.push(this.createPageButton(i, i === this.currentPage));
                    }

                    if (this.currentPage < totalPages - 3) {
                        pages.push('<span>...</span>');
                    }

                    // Always show last page
                    pages.push(this.createPageButton(totalPages, this.currentPage === totalPages));
                }

                container.innerHTML = pages.join('');
            }

            createPageButton(pageNum, isActive) {
                const className = `pagination-btn ${isActive ? 'active' : ''}`;
                return `<button class="${className}" onclick="logsManager.changePage(${pageNum})">${pageNum}</button>`;
            }

            changePage(page) {
                if (page < 1 || page > Math.ceil(this.totalLogs / this.pageSize)) return;
                this.currentPage = page;
                this.renderLogs();
            }

            updateStats() {
                const now = new Date();
                const today = now.toISOString().split('T')[0];
                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

                const todayLogs = this.allLogs.filter(log => {
                    const logDate = log.changed_at ? new Date(log.changed_at).toISOString().split('T')[0] : '';
                    return logDate === today;
                }).length;

                const weekLogs = this.allLogs.filter(log => {
                    const logDate = log.changed_at ? new Date(log.changed_at).toISOString().split('T')[0] : '';
                    return logDate >= weekAgo;
                }).length;

                document.getElementById('total-logs').textContent = this.allLogs.length;
                document.getElementById('today-logs').textContent = todayLogs;
                document.getElementById('week-logs').textContent = weekLogs;
            }

            async exportLogs() {
                const format = document.querySelector('input[name="export-format"]:checked').value;
                const logsToExport = this.filteredLogs.length > 0 ? this.filteredLogs : this.allLogs;

                if (logsToExport.length === 0) {
                    alert('No logs to export');
                    return;
                }

                try {
                    if (format === 'csv') {
                        this.exportAsCSV(logsToExport);
                    } else if (format === 'json') {
                        this.exportAsJSON(logsToExport);
                    }
                } catch (error) {
                    console.error('Export failed:', error);
                    alert('Export failed. Please try again.');
                }
            }

            exportAsCSV(logs) {
                const headers = ['Time', 'Assignment ID', 'Vessel Name', 'Jetty Name', 'Old Start Time', 'New Start Time', 'Old End Time', 'New End Time', 'Reason'];
                const csvContent = [
                    headers.join(','),
                    ...logs.map(log => [
                        this.formatDateTime(log.changed_at),
                        log.assignment_id || '',
                        `"${log.vessel_name || ''}"`,
                        `"${log.jetty_name || ''}"`,
                        this.formatDateTime(log.old_start_time),
                        this.formatDateTime(log.new_start_time),
                        this.formatDateTime(log.old_end_time),
                        this.formatDateTime(log.new_end_time),
                        `"${(log.reason || '').replace(/"/g, '""')}"`
                    ].join(','))
                ].join('\n');

                this.downloadFile(csvContent, 'logs.csv', 'text/csv');
            }

            exportAsJSON(logs) {
                const jsonContent = JSON.stringify(logs, null, 2);
                this.downloadFile(jsonContent, 'logs.json', 'application/json');
            }

            downloadFile(content, filename, mimeType) {
                const blob = new Blob([content], { type: mimeType });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }

            formatDateTime(dateString) {
                if (!dateString) return '';
                try {
                    const date = new Date(dateString);
                    return date.toLocaleString();
                } catch (e) {
                    return dateString;
                }
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            showLoading() {
                const tbody = document.getElementById('logs-table-body');
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="loading-state">
                            <i class="fas fa-spinner loading-spinner"></i>
                            <div>Loading logs...</div>
                        </td>
                    </tr>
                `;
            }

            showError(message) {
                const tbody = document.getElementById('logs-table-body');
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="empty-state">
                            <i class="fas fa-exclamation-triangle"></i>
                            <div>${message}</div>
                        </td>
                    </tr>
                `;
            }
        }

        // Initialize logs manager when DOM is loaded
        let logsManager;
        document.addEventListener('DOMContentLoaded', function() {
            logsManager = new LogsManager();
        });

        // Make logsManager globally available for pagination buttons
        window.logsManager = logsManager;
    </script>
{% endblock %}